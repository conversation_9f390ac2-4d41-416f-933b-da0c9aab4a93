# -*- coding: utf-8 -*-
'''
自动读取虎符数据集成任务，同步到d2创建，根据源数据类型执行不同策略
Sap --> 单表离线同步
Mysql,sqlserver --整库同步（离线|实时）


Step1： 读取虎符数据集成任务
    1. 基于数据源id获取所有数据同步表
    2. 基于数据源类型确定创建D2任务的不同分支
Step2:  创建D2数据同步任务
    1. 调用maxcompute 接口建表  cfdata
    2. 调用dataworks 接口创建任务并冒烟测试
Step3:  dryrun
    1. 完善调度信息，发布上线
'''

import logging
import re
from urllib.parse import quote_plus

from auto_etl import generate_dw_table, get_ddl_from_mssql
from config import get_config
from utils import get_logger
from xuelangos.hufu import get_dsconfig_by_id, get_field_mapping, get_hufu_sync_cols_mapping
from dataworks.dataworks import D2
from utils.db_utils import get_pg_query, get_mysql_query, get_hana_query, get_oracle_query
from utils.db_utils import get_mc_query
from odps import ODPS
import os

# 初始化ODPS客户端
o = ODPS(
    access_id=os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),
    secret_access_key=os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),
    project='cfdata',
    endpoint='https://service.cn-hangzhou.maxcompute.aliyun.com/api',
)
table_list = o.list_tables(project='cfdata', schema='cfsap')
table_name_list = [table.name for table in table_list]
logger = get_logger(__name__)





def create_odps_table_from_hufu(source_ds_id, schema, table_raw_name):
    """
    从虎符读取source表的源系统数据，转换为odps建表语句，如odps已存在表则忽略，否则创建odps表

    Args:
        source_ds_id: 源数据源ID
        schema: 源表schema
        table_raw_name: 源表名
        target_table: 目标表名
    """

    
    # 检查表是否已存在
    target_schema = 'CFHR' if source_ds_id == 67 else 'CFSAP'
    target_table = f'{target_schema}.ODS_{table_raw_name}'
    target_schema,target_raw_table = target_table.split('.')
    if not target_raw_table:
        logger.info(f"target table {target_table}")
    if target_raw_table.lower() in table_name_list:
        logger.info(f"ODPS表 {target_table} 已存在，跳过创建")
        return
    config = get_dsconfig_by_id(source_ds_id)

    table_name = table_raw_name
    if 'dbType' not in config:
        logging.error('dbType is required')
        return

    match config['dbType']:
        case 'MySQL':
            # db,table_raw_name  = table_name.split('.')
            col_query = f'''
            show columns from {table_raw_name};
            '''
            col_query = f'''
            SELECT upper(COLUMN_NAME), case
           when DATA_TYPE in ('nvarchar', 'varchar', 'char') then concat(DATA_TYPE, '(', CHARACTER_MAXIMUM_LENGTH , ')')
           when DATA_TYPE in ('decimal', 'numeric') then concat('decimal(', NUMERIC_PRECISION , ',', NUMERIC_SCALE, ')')
           --	 when t.name in ('smallint','tinyint') then 'int'
           when DATA_TYPE IN ('text' , 'longtext') then 'STRING'
           when DATA_TYPE IN ('mediumtext') then 'STRING'
           when DATA_TYPE = 'bit' then 'BOOLEAN'
           --	 when t.name = 'varbinary' then 'BLOB'
           else DATA_TYPE
           end as data_type, IS_NULLABLE, COLUMN_COMMENT
            FROM information_schema.columns
            WHERE table_schema = '{schema}'
              AND table_name = '{table_raw_name}'
            ORDER BY ORDINAL_POSITION
            '''
            col_str = get_mysql_query(col_query,config)
            col_dtl_str = []
            for col_name, col_type, is_nullable, comments in col_str:
                comment = comments.replace('\'', '')
                col_dtl = f''' {col_name}  {col_type}  {'not null' if is_nullable == 'NO' else ''} comment '{comment}' '''
                col_dtl_str.append(col_dtl)

            pk_query = f'''
            SELECT group_concat(column_name SEPARATOR ',')
            FROM information_schema.key_column_usage
            WHERE table_schema = '{schema}'
            AND table_name =  '{table_raw_name}'
            AND constraint_name = 'PRIMARY';
            '''
            pk_res = get_mysql_query(pk_query, config)
            if pk_res:
                pk = pk_res[0][0]
            if pk:
                pk_str = f', PRIMARY KEY ({pk})'
            table_comment = f'''
                SELECT TABLE_COMMENT
                FROM INFORMATION_SCHEMA.TABLES
                WHERE table_schema = '{schema}'
            AND table_name =  '{table_raw_name}';
            '''
            table_comment = get_mysql_query(table_comment, config)
            if table_comment:
                table_comment_str = table_comment[0][0]
            else:
                table_comment_str = ''
            target_table = f'{config['target_schema']}.ODS_{table_raw_name}'
            table_ddl = f'''
            CREATE COLUMN TABLE  {target_table} (
            {",\n".join(col_dtl_str)}
            {'' if pk is None else pk_str}
            ) COMMENT '{table_comment_str}';
                
            '''
            logger.info(table_ddl)
            get_mc_query(table_ddl)  # 建表
            return (table_name, target_table)
        case 'SQLServer':
            db, schema, table_raw_name = table_name.split('.')
            pass
            return ()
        case 'Sap':
            if not config['server']:
                config = get_config()['db.h4p']
            # table_raw_name = table_name.upper().split('.')
            table_query = f'''
            SELECT object_type, object_oid
            FROM objects
            WHERE schema_name = '{schema}'
              AND OBJECT_NAME = '{table_raw_name}'
            
            '''
            table_info = get_hana_query(table_query, _config=config)
            if table_info:
                table_type, table_oid = table_info[0]
            else:
                logger.error(f'table {schema}.{table_raw_name} not found')
                return
            if table_type == 'TABLE':
                col_query = f'''
                  select
                    TC.POSITION,
                    fieldname,
                    CASE WHEN TC.DATA_TYPE_NAME IN ('DECIMAL') THEN TC.DATA_TYPE_NAME || '('||LENGTH||CASE WHEN SCALE IS NOT NULL THEN ',' || SCALE ELSE '' END||')'
                         WHEN TC.DATA_TYPE_NAME IN ('FLOAT','VARCHAR', 'NVARCHAR', 'ALPHANUM', 'SHORTTEXT','VARBINARY') THEN TC.DATA_TYPE_NAME || '('||LENGTH||')'
                    ELSE TC.DATA_TYPE_NAME END 
                    AS DATA_TYPE_NAME,
                    IS_NULLABLE,
                    ddtext
                from saphanadb.dd03l col
                    inner join table_columns tc on col.tabname = tc.table_name and col.FIELDNAME = tc.column_name and tc.table_oid = '{table_oid}'
                left join saphanadb.dd04t t on col.ROLLNAME = t.ROLLNAME and t.DDLANGUAGE = '1'
                where fieldname NOT like_REGEXPR '/'
                ORDER BY TC.POSITION
                ;
                '''
            else:  # view
                col_query = f'''
                SELECT POSITION,
                       COLUMN_NAME,
                       CASE
                            WHEN DATA_TYPE_NAME IN ('DECIMAL') THEN DATA_TYPE_NAME || '(' || LENGTH ||
                                                   CASE WHEN SCALE IS NOT NULL THEN ',' || SCALE ELSE '' END
                                                       || ')'
                            WHEN DATA_TYPE_NAME IN ('FLOAT', 'VARCHAR', 'NVARCHAR', 'ALPHANUM', 'SHORTTEXT', 'VARBINARY')
                            THEN DATA_TYPE_NAME || '(' || LENGTH || ')'
                            ELSE DATA_TYPE_NAME END AS DATA_TYPE_NAME,
                       IS_NULLABLE,
                       comments
                FROM VIEW_columns
                WHERE VIEW_OID = '{table_oid}'
                AND  COLUMN_NAME NOT like_REGEXPR '/'
                order by POSITION
                ;
                '''
            col_str = get_hana_query(col_query, _config=config)
            col_dtl_str = []
            for position, col_name, col_type, is_nullable, comments in col_str:
                col_name = col_name.replace('/','_') # 转换特殊字符
                comment = comments.replace('\'', '') if comments else ''
                odps_col_type = _convert_to_odps_type(col_type)
                col_dtl = f''' {col_name}  {odps_col_type}  {'not null' if is_nullable == 'FALSE' else ''} comment '{comment}' '''
                col_dtl_str.append(col_dtl)
            # col_dtl_str.append('_PARTITIONTIME  timestamp ')
            pk_query = f'''
            select string_agg(column_name,','order by position asc) 
            from constraints
                where is_primary_key = 'TRUE'
                and SCHEMA_NAME = '{schema}'
                  AND TABLE_NAME = '{table_raw_name}'
            '''
            pk_res = get_hana_query(pk_query, _config=config)
            pk = None
            if pk_res:
                pk = pk_res[0][0]
                if pk:
                    pk_str = f', PRIMARY KEY ({pk})'
            table_comment = f'''
                SELECT COALESCE(DDTEXT, COMMENTS) AS TABLE_COMMENTS
                FROM TABLES t
                         LEFT JOIN saphanadb.dd02t ts
                                   ON t.table_name = ts.TABNAME AND ts.DDLANGUAGE = '1' AND t.SCHEMA_NAME = 'SAPHANADB'
                WHERE t.table_oid = '{table_oid}'
                UNION ALL
                SELECT comments
                FROM views
                WHERE view_oid = '{table_oid}'
            '''
            table_comment = get_hana_query(table_comment, _config=config)
            if table_comment:
                table_comment_str = table_comment[0][0]

            table_ddl = f'''
            CREATE  OR REPLACE TABLE  {target_table} (
            {",\n".join(col_dtl_str)}
          --  {'' if pk is None else pk_str}
            ) COMMENT '{table_comment_str}' 
            PARTITIONED BY (ds string comment '分区字段')
--             tblproperties (
--             "lifecycle.deletemeta"="false",
--                 "transactional"="true", 
--                 "write.bucket.num" = "8", 
--                 "acid.data.retain.hours"="72",
--                 "acid.cdc.mode.enable"="true",
--                 "acid.cdc.build.async"="true",
--                "acid.cdc.build.interval"="300",
--                "odps.storage.orc.enable.memcmp.sort.key="true"
--             )
            lifecycle  7
            ;
                
            '''
            get_mc_query(table_ddl)  # 建表
            # logger.info(table_ddl)
            return (table_name, target_table)
        case 'PostgreSQL':
            db, schema, table_raw_name = table_name.lower().split('.')
            col_query = f'''
        SELECT
    col.column_name,
    CASE
        WHEN col.data_type = 'integer' THEN 'INTEGER'
        WHEN col.data_type = 'bigint' THEN 'BIGINT'
        WHEN col.data_type = 'numeric' THEN 'DECIMAL('||col.numeric_precision||','||col.numeric_scale||')'
        WHEN col.data_type IN ('character varying', 'varchar') THEN 'VARCHAR('||coalesce(col.character_maximum_length,5000)||')'
        WHEN col.data_type = 'text' THEN 'NCLOB'
        WHEN col.data_type = 'boolean' THEN 'BOOLEAN'
       WHEN col.data_type = 'date' THEN 'DATE'
        WHEN col.data_type LIKE 'time%' THEN 'TIME'
        WHEN col.data_type = 'json' THEN 'CLOB'
        WHEN col.data_type = 'bytea' THEN 'VARBINARY(5000)'
        ELSE 'NVARCHAR(5000)'
        END as data_type,
    col.is_nullable,
    COALESCE(col_description(c.oid, col.ordinal_position), '') as column_comment
FROM information_schema.columns col
         JOIN pg_catalog.pg_class c ON c.relname = col.table_name
         JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace AND n.nspname = col.table_schema
        '''
            # 主键查询
            pk_query = f'''
        SELECT a.attname
        FROM pg_index i
        JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
        WHERE i.indrelid = '{schema}.{table_raw_name}'
          AND i.indisprimary;
        '''

            # 表注释查询
            comment_query = f'''
        SELECT obj_description('{schema}.{table_raw_name}'::regclass, 'pg_class');
        '''

            try:
                # 处理列信息
                cols = get_pg_query(col_query, config)
                col_defs = [
                    f'"{name}" {dtype} {"NOT NULL" if is_nullable == "NO" else ""} '
                    f"COMMENT '{comment}'"
                    for name, dtype, is_nullable, comment in cols
                ]

                # 处理主键
                pks = get_pg_query(pk_query, config)
                pk_str = f', PRIMARY KEY ({", ".join([pk[0] for pk in pks])})' if pks else ''

                # 处理表注释
                table_comment = get_pg_query(comment_query, config)[0][0] or ''

                # 生成HANA DDL
                target_table = f"{config['target_schema']}.ODS_{table_raw_name.upper()}"
                ddl = f'''
                    CREATE COLUMN TABLE {target_table} (
                        {',\n'.join(col_defs)}
                        {pk_str}
                    ) COMMENT '{table_comment.replace("'", "''")}';
                    '''

                # 执行建表
                get_hana_query(ddl)
                logger.info(f"Created table: {target_table}")
                return (table_name, target_table)

            except Exception as e:
                logger.error(f"PostgreSQL DDL生成失败: {str(e)}")
                return (None, None)
        case 'Oracle':
            col_query = f'''
        SELECT c.column_name,
       data_type,
       data_length,
       c.NULLABLE,
       comments
        FROM all_tab_cols c
                 LEFT JOIN all_col_comments cc
                           ON c.table_name = cc.table_name AND c.column_name = cc.column_name
        WHERE c.table_name = '{table_name}'
        AND c.owner = '{schema}'
        ORDER BY c.column_id
        '''
            col_str = get_oracle_query(col_query, _config=config)
            col_dtl_str = []
            for  col_name, col_type, length,is_nullable, comments in col_str:
                col_name = col_name.replace('/','_') # 转换特殊字符
                comment = comments.replace('\'', '') if comments else ''
                odps_col_type = _convert_to_odps_type(col_type)
                col_dtl = f''' {col_name}  {odps_col_type}  {'not null' if is_nullable == 'FALSE' else ''} comment '{comment}' '''
                col_dtl_str.append(col_dtl)
            # col_dtl_str.append('_PARTITIONTIME  timestamp ')
            table_comment = f'''
                select COMMENTS from ALL_TAB_COMMENTS
                where 
                    OWNER = '{schema}'
                    AND TABLE_NAME = '{table_name}'
            '''
            table_comment = get_oracle_query(table_comment, _config=config)
            if table_comment:
                table_comment_str = table_comment[0][0]

            table_comment_str = '' if not table_comment_str else table_comment_str

            table_ddl = f'''
            CREATE  OR REPLACE TABLE  {target_table} (
            {",\n".join(col_dtl_str)}
            ) COMMENT '{table_comment_str}' 
            PARTITIONED BY (ds string comment '分区字段')
--             tblproperties (
--             "lifecycle.deletemeta"="false",
--                 "transactional"="true", 
--                 "write.bucket.num" = "8", 
--                 "acid.data.retain.hours"="72",
--                 "acid.cdc.mode.enable"="true",
--                 "acid.cdc.build.async"="true",
--                "acid.cdc.build.interval"="300",
--                "odps.storage.orc.enable.memcmp.sort.key="true"
--             )
            lifecycle  7
            ;
                
            '''
            get_mc_query(table_ddl)  # 建表
            # logger.info(table_ddl)
            return (table_name, target_table)
        case _:
            pass
    
    # # 创建表
    # try:
    #     o.execute_sql(create_sql, hints={'odps.namespace.schema': 'TRUE'})
    #     logging.info(f"成功创建ODPS表: cfsap.{target_table}")
    # except Exception as e:
    #     logging.error(f"创建ODPS表失败: cfsap.{target_table}, 错误: {str(e)}")
    #     raise e


def _convert_to_odps_type(source_type,srouce_data_type='saphana'):
    """
    将源系统数据类型转换为ODPS支持的数据类型

    Args:
        source_type: 源数据类型
        target_data_type: 目标数据类型

    Returns:
        str: ODPS支持的数据类型
    """
    # 如果已经有目标数据类型，则直接使用
    type_mapping = {
        'varchar': 'STRING',
        'nvarchar': 'STRING',
        'char': 'STRING',
        'text': 'STRING',
        'ntext': 'STRING',
        'nclob':'STRING',
        'xml': 'STRING',
        'longtext': 'STRING',
        'int': 'BIGINT',
        'integer': 'BIGINT',
        'smallint': 'BIGINT',
        'tinyint': 'BIGINT',
        'bigint': 'BIGINT',
        'decimal': 'DECIMAL',
        'numeric': 'DECIMAL',
        'float': 'DOUBLE',
        'double': 'DOUBLE',
        'real': 'DOUBLE',
        'bit': 'BOOLEAN',
        'bool': 'BOOLEAN',
        'boolean': 'BOOLEAN',
        'datetime': 'DATETIME',
        'datetime2': 'DATETIME',
        'timestamp': 'DATETIME',
        'date': 'DATE',
        'time': 'STRING'
    }

    
    # 根据源数据类型进行转换
    if source_type:
        source_type = source_type.lower()
    else:
        return 'STRING'

    
    return type_mapping.get(source_type, 'STRING')


def create_d2_sync_task_from_hufu(source_ds_id, source_table, target_table, task_flow_name,task_id, cycle,is_truncate,is_inc,inc_filter):
    """
    从虎符读取元数据，转换为D2同步任务，调用D2的openapi接口创建任务并提交调度

    Args:
        source_ds_id: 源数据源ID
        source_table: 源表名，格式为 db.schema.table 或 db.table
        target_table: 目标表名
        task_flow_id: 任务流ID

    Returns:
        list: 创建的任务名称列表
    """
    # 1. 从虎符读取源数据源配置
    source_ds_config = get_dsconfig_by_id(source_ds_id)
    source_ds_type = source_ds_config['ds_type']
    source_ds_name = source_ds_config['ds_name']
    d2_src_map = {
        'SAPHANADB':'SAP_saphanadb',
        'CFDBUSER':'Sap'
    }

    
    # 2. 解析表名
    if source_table.count('.') == 2:
        db, schema, table_raw_name = source_table.split('.')
        source_raw_table = f'{schema}.{table_raw_name}'
    else:
        db, table_raw_name = source_table.split('.')
        schema = db
        source_raw_table = f'{schema}.{table_raw_name}'
    
    # 3. 创建ODPS表（如果不存在）
    create_odps_table_from_hufu(source_ds_id, schema, table_raw_name)
    # 3.1 创建dw表
    # generate_dw_table('Sap', target_table,source_ds_config)
    
    # 4. 获取字段映射信息
    col_res = get_field_mapping(source_ds_id, schema, table_raw_name)
    
    # 5. 构造字段列表
    src_columns = []
    tgt_columns = []
    for position, col_name, col_type, is_nullable, comments in col_res:
        src_columns.append(col_name)
        tgt_columns.append(col_name)
    
    src_columns_str = ','.join(src_columns)
    tgt_columns_str = ','.join(tgt_columns)
    
    # 6. 确定目标数据源信息（这里假设是ODPS）
    target_schema = 'cfhr'  # 可根据需要调整
    target_database = 'cfdata'  # 可根据需要调整
    target_schema,target_raw_table = target_table.split('.')
    tgt_type = 'odps'
    
    # 7. 确定源数据源类型（转换为D2支持的类型）
    d2_src_type_map = {
        'Sap': 'saphana',
        'MySQL': 'mysql',
        'SQLServer': 'sqlserver',
        'PostgreSQL': 'postgresql'
    }
    d2_src_type = d2_src_type_map.get(source_ds_type, source_ds_type.lower())
    d2_src_name = d2_src_map.get(schema.upper(), source_ds_name)
    
    # 8. 构造任务名称和路径
    file_name = f"imp_{db}_{table_raw_name}_df".upper()
    file_folder_path = "业务流程/ps/数据集成"  # 可根据需要调整文件夹路径



    # 9. 创建D2实例
    d2_client = D2()
    odps_file_name = f"{file_name}_TO_ODPS"
    cycle_mapping = {
        0: 'M',   # 月
        1: 'W',   # 周
        2: 'D',   # 天
        3: 'H',   # 小时
        4: 'Min'  # 分钟
    }
    hufu_cycle = cycle_mapping.get(cycle, 'D')
    hufu_file_name = f"{task_flow_name}_{source_ds_name}_{source_raw_table}_{hufu_cycle}"

    # 10. 创建两个同步任务：一个到ODPS，一个到原Hufu目标数据源

    # 10.1 创建到ODPS的同步任务
    if not d2_client.node_exists(file_folder_path,odps_file_name):
        D2.create_d2_sync_task(
            d2_client,
            input_list=[],  # 可根据需要添加依赖
            FileFolderPath=file_folder_path,
            FileName=odps_file_name,
            src_type=d2_src_type,
            src_name=d2_src_name,
            src_columns=src_columns_str,
            src_table=source_raw_table,
            tgt_type=tgt_type,
            tgt_columns=tgt_columns_str,
            tgt_table=target_raw_table,
            target_schema=target_schema,
            target_database=target_database
        )
    else:
        logger.info(f"D2任务已存在: {file_folder_path}/{odps_file_name}")
    #
    # 10.2 创建到原Hufu目标数据源的同步任务（这里以SAP为例）
    hufu_tgt_type = 'saphana'
    hufu_tgt_name = 'HDW_CFHR'  # 原Hufu中的目标数据源名称



    hufu_file_folder_path = "业务流程/hufu/数据集成"
    hufu_src_cols,hufu_tgt_cols = get_hufu_sync_cols_mapping(task_id)
    if not d2_client.node_exists(hufu_file_folder_path,hufu_file_name):
            D2.create_d2_sync_task(
            d2_client,
            input_list=[],  # 可根据需要添加依赖
            FileFolderPath=hufu_file_folder_path,
            FileName=hufu_file_name,
            src_type=d2_src_type,
            src_name=d2_src_name,
            src_columns=hufu_src_cols,
            src_table=source_raw_table,
            tgt_type=hufu_tgt_type,
            tgt_columns=hufu_tgt_cols,
            tgt_table=target_raw_table,
            target_schema=target_schema,
            target_database=hufu_tgt_name,
            is_truncate = is_truncate,
            where = inc_filter
        )
    else:
        logging.info(f"D2任务已存在: {hufu_file_folder_path}/{hufu_file_name}")
    
    return (odps_file_name ,hufu_file_name) # 返回创建的任务名称列表





def batch_create_d2_sync_tasks_from_hufu(source_ds_id):
    """
    批量从虎符创建D2同步任务

    Args:
        source_ds_id: 源数据源ID

    Returns:
        list: 创建的任务ID列表
    """
    # 查询虎符中指定数据源的所有同步任务
    query = f'''
    -- 任务查询
    select
        tf.id as task_flow_id,
        tf.name as task_flow_name,
        task.id as task_id,
        sch.cycle, -- 周期
        tf.remark, -- 工作流备注
--         tf.xxl_job_id,
--         tf.update_time as job_update_time, --最后更新时间
    --    tf.publish_time as job_publish_time, --发布时间
--         task.name , -- 节点名称
    --    case when v.down_node_id is not null and tf.is_locked=0 then '已调度' else '未调度' end as sch_stats,
--         task.update_time as task_update_time, -- 最后更新时间
    -- sc.script ,
        sync.source_ds_type ,
        ss.ds_name source_ds_name,
        ss.ds_desc as source_ds_desc,
        ss.ds_link as source_ds_link,
        sync.source_table ,
        sync.target_ds_type ,
        st.ds_name as target_ds_name,
        st.ds_desc as target_ds_desc,
        st.ds_link as target_ds_link,
        sync.target_table ,
        sync.data_replace_rule_id ,
        sync.is_truncate ,
        sync.is_inc ,
        sync.inc_filter
    from  hufu.tb_datadev_menunode tf
    left join HUFU.tb_datadev_sub_node n on tf.id = n.task_flow_id and n.row_state  = 1 and n.row_state =1
    left join hufu.tb_datadev_menunode task on n.node_id  = task.id and task.row_state  =1
    left join hufu.tb_datadev_offline_sync sync on task.id = sync.node_id and task.node_type = 20 -- 20 离线同步 13 sql 12 虚拟节点
    left join hufu.tb_datasource ss on sync.source_ds_id  = ss.id
    left join hufu.tb_datasource st on sync.target_ds_id  = st.id
    inner join (
        select down_node_id
        from hufu.tb_datadev_sub_node_dependency
        where row_state =1
    )v on task.id = v.down_node_id
    inner join (
        select tf.id,v.cycle
        from hufu.tb_datadev_vnode v
                          inner join hufu.tb_datadev_menunode m on v.node_id = m.id and m.node_type =12
                          inner join hufu.tb_datadev_menunode tf on m.pid = tf.id and tf.node_type = 1 and tf.row_state = 1
    )sch on sch.id = tf.id
    where 1=1
      and tf.node_type = 1
      and tf.row_state =1
      and tf.is_locked = 0
      and tf.publish_time is not null
--       and sync.source_ds_id in (10,37) -- SAP
      and sync.source_ds_id in (67)
      and sync.target_ds_id in (39,68)
      and sch.cycle = 2 
    -- and st.id = 65
    ;
    '''
    
    results = get_pg_query(query)
    task_ids = []



    for task_flow_id, task_flow_name,task_id, cycle, remark, source_ds_type, source_ds_name, source_ds_desc, source_ds_link, source_table, target_ds_type, target_ds_name, target_ds_desc, target_ds_link, target_table, data_replace_rule_id, is_truncate, is_inc, inc_filter in results:
        try:
            truncate = True if is_truncate == 1 else False
            tid = create_d2_sync_task_from_hufu(source_ds_id, source_table, target_table, task_flow_name,task_id, cycle,truncate,is_inc,inc_filter)
            db, table_raw_name = source_table.split('.')
            # tid = create_odps_table_from_hufu(source_ds_id, db, table_raw_name)
            task_ids.append(tid)

        except Exception as e:
            logger.error(f"创建任务失败: source_table={source_table}, error={str(e)}")
    
    return task_ids

def batch_create_hana_sql_from_hufu():
    query = f'''
    -- 任务查询
           select
            tf.id as task_flow_id,
            tf.name as task_flow_name,
            task.name as task_name,
            task.id as task_id,
            sch.cycle, -- 周期
            tf.remark, -- 工作流备注
        --         tf.xxl_job_id,
        --         tf.update_time as job_update_time, --最后更新时间
            --    tf.publish_time as job_publish_time, --发布时间
        --         task.name , -- 节点名称
            --    case when v.down_node_id is not null and tf.is_locked=0 then '已调度' else '未调度' end as sch_stats,
        --         task.update_time as task_update_time, -- 最后更新时间
            -- sc.script ,
            sc.script
        from  hufu.tb_datadev_menunode tf
                  left join HUFU.tb_datadev_sub_node n on tf.id = n.task_flow_id and n.row_state  = 1 and n.row_state =1
                  left join hufu.tb_datadev_menunode task on n.node_id  = task.id and task.row_state  =1
                  left join hufu.tb_datadev_script sc on task.id = sc.node_id and task.node_type =13
                  left join hufu.tb_datadev_offline_sync sync on task.id = sync.node_id and task.node_type = 20 -- 20 离线同步 13 sql 12 虚拟节点
                  left join hufu.tb_datasource ss on sync.source_ds_id  = ss.id
                  left join hufu.tb_datasource st on sync.target_ds_id  = st.id
                  inner join (
            select down_node_id
            from hufu.tb_datadev_sub_node_dependency
            where row_state =1
        )v on task.id = v.down_node_id
                  inner join (
            select tf.id,v.cycle
            from hufu.tb_datadev_vnode v
                     inner join hufu.tb_datadev_menunode m on v.node_id = m.id and m.node_type =12
                     inner join hufu.tb_datadev_menunode tf on m.pid = tf.id and tf.node_type = 1 and tf.row_state = 1
        )sch on sch.id = tf.id
        where 1=1
          and tf.node_type = 1
          and tf.row_state =1
          and tf.is_locked = 0
          and tf.publish_time is not null
          and   task.node_type =13
        -- and st.id = 65
        ;
    '''

    results = get_pg_query(query)
    task_ids = []


    d2 = D2()
    file_folder_path = '业务流程/hufu/数据库'
    for task_flow_id, task_flow_name,task_name, task_id,cycle, remark, script in results:
        try:
            # task_id = create_d2_sync_task_from_hufu(source_ds_id, source_table, target_table, task_flow_name, cycle)
            file_name = f'{task_flow_name}.{task_name}'
            task_id = d2.create_hana_task(file_folder_path,file_name,script)
            task_ids.append(task_id)

        except Exception as e:
            logger.error(f"创建任务失败: {task_flow_name}||{task_name}, error={str(e)}")

    return task_ids





if __name__ == '__main__':
    res = batch_create_d2_sync_tasks_from_hufu(67)
    # d2 = D2()
    # file_id_list=[529586617,529586619]
    # # # 529569275
    # # d2.update_file(file_id)
    # # # d2.submit_file(file_id)
    # for file_id in file_id_list:
    #     d2.delete_file(file_id)
    # res = create_d2_sync_task_from_hufu(10, 'SAPHANADB.cdpos', 'CFSAP.ODS_cdpos', 'PRO_D_H4P_HANA_变更日志', 2)
    # source_ds_id, schema, table_raw_name, target_table = 10, 'SAPHANADB', 'ZTFI007', 'ODS_ZTFI007'
    # create_odps_table_from_hufu(10, 'CFDBUSER', 'FR_PP_IN_SX_DTL_V')
    # print(res)
    # for table in table_name_list:
    #     if table.endswith('_df'):
    #         print(table)
    #         o.delete_table(table, 'cfdata',if_exists=True,schema='cfsap');


