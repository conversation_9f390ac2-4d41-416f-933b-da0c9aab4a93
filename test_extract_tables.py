#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script to demonstrate how to use the table extraction functions.
"""

import os
from utils import get_logger
from fine_report.finereport import extract_dm_sql_tables, extract_cpt_tables
from utils.sql_table_extractor import extract_all_tables_from_sql, extract_tables_by_schema

logger = get_logger(__name__)

def test_extract_tables_from_sql():
    """Test extracting tables from a SQL query."""
    test_sql = """
    SELECT a.column1, b.column2, c.column3
    FROM cfdms.table1 a
    JOIN other_schema.table2 b ON a.id = b.id
    JOIN cfdms.table3 c ON a.id = c.id
    WHERE a.column1 = 'value'
    """
    
    # Extract all tables
    all_tables = extract_all_tables_from_sql(test_sql)
    logger.info(f"All tables: {all_tables}")
    
    # Extract cfdms tables
    cfdms_tables = extract_dm_sql_tables(test_sql, 'cfdms')
    logger.info(f"CFDMS tables: {cfdms_tables}")
    
    # Extract tables with a different schema
    other_tables = extract_tables_by_schema(test_sql, 'other_schema')
    logger.info(f"Other schema tables: {other_tables}")

def test_extract_tables_from_cpt(cpt_file_path):
    """Test extracting tables from a CPT file."""
    if not os.path.isfile(cpt_file_path):
        logger.error(f"File not found: {cpt_file_path}")
        return
    
    # Extract cfdms tables from the CPT file
    cfdms_tables = extract_cpt_tables(cpt_file_path, 'cfdms')
    logger.info(f"CFDMS tables in CPT file: {cfdms_tables}")
    
    # Extract tables with a different schema
    other_tables = extract_cpt_tables(cpt_file_path, 'other_schema')
    logger.info(f"Other schema tables in CPT file: {other_tables}")

if __name__ == "__main__":
    # Test SQL extraction
    test_extract_tables_from_sql()
    
    # Test CPT file extraction (provide a valid CPT file path)
    # Example: test_extract_tables_from_cpt("path/to/your/report.cpt")
    
    # You can uncomment and modify the line below with a valid CPT file path
    # test_extract_tables_from_cpt("D:/work/projects/cffr/CFMOTO/SAP/SAP整车对账单.cpt")
