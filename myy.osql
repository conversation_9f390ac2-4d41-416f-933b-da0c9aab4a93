set odps.namespace.schema=TRUE ;
--create schema cfps;
select * from cfdata.cfps.ods_ps_cf_ceta_inf_vw
select COUNT(*) from cfdata.cfsap.ods_kna1;

-- select * from cfdata.default.ods_cfdms_tm_company
-- where ds = 'mysql_dms'
-- limit 100 ;


CREATE  OR REPLACE TABLE  CFSAP.ODS_CDHDR (
  MANDANT  STRING  not null comment '集团' ,
  OBJECTCLAS  STRING  not null comment '对象类' ,
  OBJECTID  STRING  not null comment '对象值' ,
  CHANGENR  STRING  not null comment '文档的更改编号' ,
  USERNAME  STRING  not null comment '修改文档中的个人负责的用户名' ,
  UDATE  STRING  not null comment '创建修改文档的数据' ,
  UTIME  STRING  not null comment '时间已更改' ,
  TCODE  STRING  not null comment '已在其中进行更改的事务' ,
  PLANCHNGNR  STRING  not null comment '计划更改号码' ,
  ACT_CHNGNO  STRING  not null comment '修改本次修改所创建文档的数量' ,
  WAS_PLANND  STRING  not null comment '标志：从计划更改创建的更改' ,
  CHANGE_IND  STRING  not null comment '应用程序对象的更改类型' ,
  LANGU  STRING  not null comment '语言代码' ,
  VERSION  STRING  not null comment '三字节字段' ,
  _DATAAGING  STRING  not null comment 'Data Filter Value for Data Aging'
--  , PRIMARY KEY (MANDANT,OBJECTCLAS,OBJECTID,CHANGENR)
) COMMENT '更改凭证抬头'
PARTITIONED BY (ds string comment '分区字段')
--             tblproperties (
--             "lifecycle.deletemeta"="false",
--                 "transactional"="true",
--                 "write.bucket.num" = "8",
--                 "acid.data.retain.hours"="72",
--                 "acid.cdc.mode.enable"="true",
--                 "acid.cdc.build.async"="true",
--                "acid.cdc.build.interval"="300",
--                "odps.storage.orc.enable.memcmp.sort.key="true"
--             )
lifecycle  7
;


select * from cfsap.ods_v_material
--where ds = '20250821'
limit 100
;


select count(*) from cfdata.cfsap.ods_v_acdoca
where ds = '20250822'
limit 100
;