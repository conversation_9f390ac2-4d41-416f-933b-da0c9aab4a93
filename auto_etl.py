# -*- coding: utf-8 -*-
'''
自动ETL创建
Step1： 自动表创建
    流程自动感知 --》 获取附件查询（sql） --》解析待同步表 --》提取未同步表 --》 表创建
                                                                      获取源表DDL --》 自动类型转换 --》 hana 建表
Step2:  创建虎符数据同步任务
    根据流程获取待同步表来源 --》 元数据获取可添加工作流 --》 最短节点添加末端节点 --》 创建数据同步任务
Step3:  dryrun
    登录虎符运行数据同步任务
'''
import logging
import re
from urllib.parse import quote_plus

from sqllineage.core.metadata.sqlalchemy import SQLAlchemyMetaDataProvider
from sqllineage.runner import LineageRunner

from config import get_config
from utils import get_logger
from utils.db_utils import get_hana_query, get_mssql_query, get_mysql_query, get_pg_query
from xuelangos.hufu import create_sync_hufu,get_dsconfig_by_server


def get_tables_from_query(query, ds_type, config: dict = None) -> list:
    pattern1 = r'\$\{(.+)\}'
    pattern2 = r'top(\s)+\d+'
    re_query = re.sub(pattern1, '', query)
    re_query = re.sub(pattern2, '', re_query)
    dialect = 'ansi'
    provider = None
    match ds_type:
        case 'PostgreSQL':
            dialect = 'postgres'
        case 'MySQL':
            # _url = f"mysql+pymysql://{config['username']}:{quote_plus(config['password'])}@{quote_plus(config['server'])}:{config['port']}/{config['dbName']}"
            # provider = SQLAlchemyMetaDataProvider(url=_url)
            dialect = 'mysql'
        case 'Sap':
            _url = f'hana://{config['username']}:{quote_plus(config['password'])}@{config['server']}:{config['port']}'
            provider = SQLAlchemyMetaDataProvider(url=_url)
        case 'oracle':
            dialect = 'oracle'

        case 'SQLServer':
            # _url = f'mysql+pymysql://{config['username']}:{config['password']}@{config['server']}:{config['port']}/{config['dbName']}'
            # provider = SQLAlchemyMetaDataProvider(url=_url)
            dialect = 'ansi'
        case _:
            pass

    if provider:
        table_list = LineageRunner(re_query, metadata_provider=provider).source_tables
    else:
        table_list = LineageRunner(re_query, dialect=dialect).source_tables
    return table_list


def get_ddl_normal(table_name, config: dict) -> tuple:
    if 'dbType' not in config:
        logging.error('dbType is required')
        return (None, None)

    match config['dbType']:
        case 'MySQL':
            db,table_raw_name  = table_name.split('.')
            col_query = f'''
            show columns from {table_raw_name};
            '''
            col_query = f'''
            SELECT upper(COLUMN_NAME), case
           when DATA_TYPE in ('nvarchar', 'varchar', 'char') then concat(DATA_TYPE, '(', CHARACTER_MAXIMUM_LENGTH , ')')
           when DATA_TYPE in ('decimal', 'numeric') then concat('decimal(', NUMERIC_PRECISION , ',', NUMERIC_SCALE, ')')
           --	 when t.name in ('smallint','tinyint') then 'int'
           when DATA_TYPE IN ('text' , 'longtext') then 'nclob'
           when DATA_TYPE IN ('mediumtext') then 'blob'
           when DATA_TYPE = 'bit' then 'BOOLEAN'
           --	 when t.name = 'varbinary' then 'BLOB'
           else DATA_TYPE
           end as data_type, IS_NULLABLE, COLUMN_COMMENT
            FROM information_schema.columns
            WHERE table_schema = '{db}'
              AND table_name = '{table_raw_name}'
            ORDER BY ORDINAL_POSITION
            '''
            col_str = get_mysql_query(col_query,config)
            col_dtl_str = []
            for col_name, col_type, is_nullable, comments in col_str:
                comment = comments.replace('\'', '')
                col_dtl = f''' {col_name}  {col_type}  {'not null' if is_nullable == 'NO' else ''} comment '{comment}' '''
                col_dtl_str.append(col_dtl)

            pk_query = f'''
            SELECT group_concat(column_name SEPARATOR ',')
            FROM information_schema.key_column_usage
            WHERE table_schema = '{db}'
            AND table_name =  '{table_raw_name}'
            AND constraint_name = 'PRIMARY';
            '''
            pk_res = get_mysql_query(pk_query, config)
            if pk_res:
                pk = pk_res[0][0]
            if pk:
                pk_str = f', PRIMARY KEY ({pk})'
            table_comment = f'''
                SELECT TABLE_COMMENT
                FROM INFORMATION_SCHEMA.TABLES
                WHERE table_schema = '{db}'
            AND table_name =  '{table_raw_name}';
            '''
            table_comment = get_mysql_query(table_comment, config)
            if table_comment:
                table_comment_str = table_comment[0][0]
            else:
                table_comment_str = ''
            target_table = f'{config['target_schema']}.ODS_{table_raw_name}'
            table_ddl = f'''
            CREATE COLUMN TABLE  {target_table} (
            {",\n".join(col_dtl_str)}
            {'' if pk is None else pk_str}
            ) COMMENT '{table_comment_str}';
                
            '''
            logger.info(table_ddl)
            get_hana_query(table_ddl)  # 建表
            return (table_name, target_table)
        case 'SQLServer':
            db, schema, table_raw_name = table_name.split('.')

            return get_ddl_from_mssql(db, schema, table_raw_name)
        case 'Sap':
            if not config['server']:
                config = get_config()['db.h4p']
            schema, table_raw_name = table_name.upper().split('.')
            table_query = f'''
            SELECT object_type, object_oid
            FROM objects
            WHERE schema_name = '{schema}'
              AND OBJECT_NAME = '{table_raw_name}'
            
            '''
            table_info = get_hana_query(table_query, _config=config)
            if table_info:
                table_type, table_oid = table_info[0]
            else:
                logging.error(f'table {schema}.{table_raw_name} not found')
                return (None, None)
            if table_type == 'TABLE':
                col_query = f'''
                  select
                    TC.POSITION,
                    fieldname,
                    CASE WHEN TC.DATA_TYPE_NAME IN ('DECIMAL') THEN TC.DATA_TYPE_NAME || '('||LENGTH||','||SCALE||')'
                         WHEN TC.DATA_TYPE_NAME IN ('FLOAT','VARCHAR', 'NVARCHAR', 'ALPHANUM', 'SHORTTEXT','VARBINARY') THEN TC.DATA_TYPE_NAME || '('||LENGTH||')'
                    ELSE TC.DATA_TYPE_NAME END AS DATA_TYPE_NAME,
                    IS_NULLABLE,
                    ddtext
                from saphanadb.dd03l col
                    inner join table_columns tc on col.tabname = tc.table_name and col.FIELDNAME = tc.column_name and tc.table_oid = '{table_oid}'
                left join saphanadb.dd04t t on col.ROLLNAME = t.ROLLNAME and t.DDLANGUAGE = '1'
                ORDER BY TC.POSITION
                ;
                '''
            else:  # view
                col_query = f'''
                SELECT POSITION,
                       COLUMN_NAME,
                       CASE
                           WHEN DATA_TYPE_NAME IN ('DECIMAL') THEN DATA_TYPE_NAME || '(' || LENGTH || ',' || SCALE || ')'
                           WHEN DATA_TYPE_NAME IN ('FLOAT', 'VARCHAR', 'NVARCHAR', 'ALPHANUM', 'SHORTTEXT', 'VARBINARY')
                               THEN DATA_TYPE_NAME || '(' || LENGTH || ')'
                           ELSE DATA_TYPE_NAME END AS DATA_TYPE_NAME,
                       IS_NULLABLE,
                       comments
                FROM VIEW_columns
                WHERE VIEW_OID = '{table_oid}'
                order by POSITION
                ;
                '''
            col_str = get_hana_query(col_query, _config=config)
            col_dtl_str = []
            for position, col_name, col_type, is_nullable, comments in col_str:
                comment = comments.replace('\'', '') if comments else ''
                col_dtl = f''' "{col_name}"  {col_type}  {'not null' if is_nullable == 'FALSE' else ''} comment '{comment}' '''
                col_dtl_str.append(col_dtl)

            pk_query = f'''
            select string_agg(column_name,','order by position asc) 
            from constraints
                where is_primary_key = 'TRUE'
                and SCHEMA_NAME = '{schema}'
                  AND TABLE_NAME = '{table_raw_name}'
            '''
            pk_res = get_hana_query(pk_query, _config=config)
            if pk_res:
                pk = pk_res[0][0]
            if pk:
                pk_str = f', PRIMARY KEY ({pk})'
            table_comment = f'''
                SELECT COALESCE(DDTEXT, COMMENTS) AS TABLE_COMMENTS
                FROM TABLES t
                         LEFT JOIN saphanadb.dd02t ts
                                   ON t.table_name = ts.TABNAME AND ts.DDLANGUAGE = '1' AND t.SCHEMA_NAME = 'SAPHANADB'
                WHERE t.table_oid = '{table_oid}'
                UNION ALL
                SELECT comments
                FROM views
                WHERE view_oid = '{table_oid}'
            '''
            table_comment = get_hana_query(table_comment, _config=config)
            if table_comment:
                table_comment_str = table_comment[0][0]
            target_table = f'CFSAP.ODS_{table_raw_name}'
            table_ddl = f'''
            CREATE COLUMN TABLE  {target_table} (
            {",\n".join(col_dtl_str)}
            {'' if pk is None else pk_str}
            ) COMMENT '{table_comment_str}';
                
            '''
            get_hana_query(table_ddl)  # 建表
            logger.info(table_ddl)
            return (table_name, target_table)
        case 'PostgreSQL':
            db, schema, table_raw_name = table_name.lower().split('.')
            col_query = f'''
        SELECT
    col.column_name,
    CASE
        WHEN col.data_type = 'integer' THEN 'INTEGER'
        WHEN col.data_type = 'bigint' THEN 'BIGINT'
        WHEN col.data_type = 'numeric' THEN 'DECIMAL('||col.numeric_precision||','||col.numeric_scale||')'
        WHEN col.data_type IN ('character varying', 'varchar') THEN 'VARCHAR('||coalesce(col.character_maximum_length,5000)||')'
        WHEN col.data_type = 'text' THEN 'NCLOB'
        WHEN col.data_type = 'boolean' THEN 'BOOLEAN'
       WHEN col.data_type = 'date' THEN 'DATE'
        WHEN col.data_type LIKE 'time%' THEN 'TIME'
        WHEN col.data_type = 'json' THEN 'CLOB'
        WHEN col.data_type = 'bytea' THEN 'VARBINARY(5000)'
        ELSE 'NVARCHAR(5000)'
        END as data_type,
    col.is_nullable,
    COALESCE(col_description(c.oid, col.ordinal_position), '') as column_comment
FROM information_schema.columns col
         JOIN pg_catalog.pg_class c ON c.relname = col.table_name
         JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace AND n.nspname = col.table_schema
        '''
        # 主键查询
            pk_query = f'''
        SELECT a.attname
        FROM pg_index i
        JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
        WHERE i.indrelid = '{schema}.{table_raw_name}'
          AND i.indisprimary;
        '''

        # 表注释查询
            comment_query = f'''
        SELECT obj_description('{schema}.{table_raw_name}'::regclass, 'pg_class');
        '''

            try:
             # 处理列信息
                cols = get_pg_query(col_query, config)
                col_defs = [
                    f'"{name}" {dtype} {"NOT NULL" if is_nullable == "NO" else ""} '
                    f"COMMENT '{comment}'"
                    for name, dtype, is_nullable, comment in cols
                ]

                # 处理主键
                pks = get_pg_query(pk_query, config)
                pk_str = f', PRIMARY KEY ({", ".join([pk[0] for pk in pks])})' if pks else ''

                # 处理表注释
                table_comment = get_pg_query(comment_query, config)[0][0] or ''

                # 生成HANA DDL
                target_table = f"{config['target_schema']}.ODS_{table_raw_name.upper()}"
                ddl = f'''
                    CREATE COLUMN TABLE {target_table} (
                        {',\n'.join(col_defs)}
                        {pk_str}
                    ) COMMENT '{table_comment.replace("'", "''")}';
                    '''

                # 执行建表
                get_hana_query(ddl)
                logger.info(f"Created table: {target_table}")
                return (table_name, target_table)

            except Exception as e:
                    logger.error(f"PostgreSQL DDL生成失败: {str(e)}")
                    return (None, None)

        case _:
            pass


def get_ddl_from_mssql(db, schema, table) -> tuple:
    col_query = f'''
    select
	col.ORDINAL_POSITION ,
	concat_ws('  ',
	col.COLUMN_NAME,
	case
		when col.DATA_TYPE in ('nvarchar', 'varchar', 'char') then concat(col.DATA_TYPE, '(', CASE WHEN col.CHARACTER_MAXIMUM_LENGTH = -1 THEN 5000 ELSE col.CHARACTER_MAXIMUM_LENGTH END , ')')
		when col.DATA_TYPE in ('decimal', 'numeric') then concat('decimal(', col.NUMERIC_PRECISION , ',', col.NUMERIC_SCALE, ')')
		when col.DATA_TYPE in ('datetime2') then 'datetime'
		--	 when t.name in ('smallint','tinyint') then 'int'
		when col.DATA_TYPE IN ('ntext' , 'xml') then 'nclob'
		when col.DATA_TYPE = 'bit' then 'BOOLEAN'
		--	 when t.name = 'varbinary' then 'BLOB'
		else col.DATA_TYPE
	end,
	case
		when col.is_nullable = 'NO' then 'NOT NULL'
		ELSE ''
	END,
	case
		when col_desc.col_desc is not null then concat('COMMENT "',CONVERT(varchar , col_desc.col_desc),'"')
		else ''
	end,
	',') as col_str
from
	{db}.INFORMATION_SCHEMA.COLUMNS col
left join (
	SELECT
		t.name as table_name,
		minor_id as ORDINAL_POSITION,
		max(p.value) as col_desc
	FROM
		{db}.sys.tables AS t
	INNER JOIN 
	    {db}.sys.extended_properties AS p ON
		t.object_id = p.major_id
	WHERE
		 p.name = 'MS_Description'
	group by
		t.name,
		minor_id

)col_desc on
	col.TABLE_NAME = col_desc.table_name
	and col.ORDINAL_POSITION = col_desc.ORDINAL_POSITION
where
	upper(col.TABLE_NAME)= upper('{table}')
	AND 
    lower(col.TABLE_SCHEMA) = lower('{schema}')
    order by ORDINAL_POSITION
    '''
    col_str = '\n'.join([col for ORDINAL_POSITION, col in get_mssql_query(col_query)])

    # primary key
    pk_query = f'''
    SELECT 
    c.name
FROM 
    {db}.sys.objects o
INNER JOIN 
    {db}.sys.key_constraints k ON k.parent_object_id = o.object_id
INNER JOIN 
    {db}.sys.columns c ON c.object_id = o.object_id AND c.column_id = k.unique_index_id
INNER JOIN 
	{db}.sys.schemas s on o.schema_id  = s.schema_id 
WHERE 
    o.type = 'U' AND k.type = 'PK'
  and lower(s.name) = lower('{schema}') 
  and lower(o.name) = lower('{table}')
    '''
    pk_str = ','.join([col[0] for col in get_mssql_query(pk_query)])

    table_comment_query = f'''
    	SELECT
		CONVERT(nvarchar, p.value) as value
	FROM
		{db}.sys.tables AS t
	INNER JOIN 
	    {db}.sys.extended_properties AS p ON
		t.object_id = p.major_id
	INNER JOIN 
		{db}.sys.schemas s ON t.schema_id  = s.schema_id 
	WHERE
		
		lower(t.name)  = lower('{table}')
		and lower(s.name) = lower('{schema}')
		and p.name = 'MS_Description'
		and minor_id =0
    '''
    table_comment_str = ''
    res = get_mssql_query(table_comment_query)
    if res:
        table_comment_str = res[0][0]
    logger.info(table_comment_str)
    table_ddl = f'''
    CREATE COLUMN TABLE CFBPM.ODS_{table.upper()} (
    {col_str}
    primary key ({pk_str})
    )COMMENT '{table_comment_str}';
'''.replace('"', '\'')
    return (f'CFBPM.ODS_{table.upper()}', table_ddl.upper())


def generate_dw_table(src_type, table_name, config: dict = None) -> tuple:
    logger.info(f'{table_name} will be generated')

    match src_type:
        case 'bpm':
            db, schema, table_raw_name = table_name.split('.')
            ods_table, table_ddl = get_ddl_from_mssql(db, schema, table_raw_name)
            get_hana_query(table_ddl)
            logger.info(table_ddl)
            return (table_name, ods_table)
        case _:

            return get_ddl_normal(table_name, config)


def auto_etl(query, ds_type='SQLServer', config: dict = None) -> list:
    table_list = get_tables_from_query(query, ds_type, config)
    schema_dict = {
        'MXBPMDB': 'MX',
        'MXBPMDATA': 'MX',
        'CQBPMDB': 'CQ',
        'CQBPMDATA': 'CQ',
        'JXJBPMDB': 'JXJ',
        'JXJBPMDATA': 'JXJ',
        'KTMBPMDB': 'KTM',
        'CKMBPMDB': 'CKM',

    }
    job_list = []
    for table in table_list:
        table_full_name = str(table)
        match ds_type:
            case 'SQLServer':
                db, schema, raw_name = table_full_name.upper().split('.')
                ods_table = f"ODS_{schema_dict[schema]}_{raw_name}" if schema in schema_dict and not raw_name.startswith(
                    schema_dict[schema]) else f"ODS_{raw_name}"
                query = f'''
                select 1 from sys.tables
                where schema_name = 'CFBPM'
                AND TABLE_NAME = '{ods_table}'
                '''
                res = get_hana_query(query)
                if res:
                    continue

                logger.info(query)
                source_table, target_table = generate_dw_table('bpm', table_full_name)
                job_list.append(create_sync_hufu(source_table, target_table))
            case 'Sap':
                schema, raw_name = table_full_name.upper().split('.')
                target_schema = 'CFSAP'
                ods_table = f'ODS_{raw_name}'
                query = f'''
                select 1 from sys.tables
                where schema_name = '{target_schema}'
                AND TABLE_NAME = '{ods_table}'
                '''
                res = get_hana_query(query)
                if res:
                    continue
                logger.debug(query)
                config['dbType'] = 'Sap'
                source_table, target_table = generate_dw_table('h4p', table_full_name, config)
                job_list.append(create_sync_hufu(source_table, target_table))
            case 'MySQL':
                schema, table_raw_name = table_full_name.split('.')
                target_schema = config['target_schema']
                ods_table = f'ODS_{table_raw_name.upper()}'
                query = f'''
                select 1 from sys.tables
                where schema_name = '{target_schema}'
                AND TABLE_NAME = '{ods_table}'
                '''
                res = get_hana_query(query)
                if res:
                    continue

                ds_server = config['server']
                db = config['dbName']
                ds_info = get_dsconfig_by_server(ds_server, db)
                config.update(ds_info)
                config['server'] = ds_server
                source_table, target_table = generate_dw_table(ds_info['dbType'], table_full_name, config)
                job_list.append(create_sync_hufu(source_table, target_table))
    return job_list


def dry_run(task_id):
    pass


if __name__ == '__main__':
    logger = get_logger(__name__, logging.DEBUG)
    drop_query = '''
    drop table cfsap.ODS_QAMB;
    '''
    # get_hana_query(drop_query)
    ds_type = 'MySQL'
    config = {
        'dbName':'cfdms',
        'username': 'cfmotodms',
        'password': 'Cfmoto@123DMS',
        'server': 'rm-bp11l460168e7i2hc33170vm.mysql.rds.aliyuncs.com',
        'port': 3306,
        'target_schema':'CFDMS'
    }
    # config = {
    #     'dbName':'dms_zeeho',
    #     'username': 'ztread',
    #     'password': '2025@!Zeeho',
    #     'server': 'rm-bp17mrd652my6154rpo.mysql.rds.aliyuncs.com',
    #     'port': 3306,
    #     'target_schema':'CFDMSZ'
    # }

    # config = {
    #     'dbName':'cfmoto',
    #     'username': 'tmaread',
    #     'password': 'jO0i*hu*UM',
    #     'server': 'rr-bp1d80o91717116661o.mysql.rds.aliyuncs.com',
    #     'port': 3306,
    #     'target_schema':'CFSAPBC'
    # }

    # config = {
    #     'dbName':'yx2_flats',
    #     'username': 'yx2',
    #     'password': 'CERUmKnymVvFdc5Sy4h',
    #     'server': '10.32.3.13',
    #     'port': 3306,
    #     'target_schema':'CFCOM'
    # }
    #
    # config = {
    #     'dbName':'cfeol',
    #     'username': 'root',
    #     'password': 'Cfmoto@2024',
    #     'server': '10.32.2.115',
    #     'port': 3306,
    #     'target_schema':'CFCOM'
    # }
    # config = {
    #     'dbName':'srm',
    #     'username': 'audit',
    #     'password': 'Audit@2024',
    #     'server': '10.32.2.221',
    #     'port': 9636,
    #     'target_schema':'CFSRM'
    # }

    # ds_type = 'Sap'
    # config = {
    #     'username': 'CFDBUSER',
    #     'password': 'Cfdl1235',
    #     'server': '192.168.1.207',
    #     'port': 30015,
    #     'target_schema':'CFSAP'
    # }
    query = '''
	select * from cfdms.main_bulletin_type


            '''
    table_list = auto_etl(query, ds_type,config)
    # table_list = auto_etl(query)
    logger.info(table_list)
