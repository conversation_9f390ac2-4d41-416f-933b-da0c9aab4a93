'get_table_id:h4p-sap.H4P.SAPHANADB.MAPL', (0, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_MAPL', (512, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ADR3', (1024, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ADR3', (1536, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZMMT012_FICO', (2048, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZMMT012_FICO', (2560, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.LFC1', (3072, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_LFC1', (3584, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.QMFE', (4096, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_QMFE', (4608, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T025T', (5120, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T025T', (5632, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.QMEL', (6144, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_QMEL', (6656, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T300T', (7168, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T300T', (7680, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T001L', (8192, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T001L', (8704, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ADRP', (9216, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ADRP', (9728, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TABW', (10240, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TABW', (10752, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_CUSTOMER_SALES', (11264, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_CUSTOMER_SALES', (11776, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TVGRT', (12288, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TVGRT', (12800, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.MBEW', (13312, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_MBEW_FR', (13824, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.KNA1', (14336, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_KNA1', (14848, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_ZMMT_BC_19', (15360, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_ZMMT_BC_19', (15872, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TVAKT', (16384, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TVAKT', (16896, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.KONM', (17408, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_KONM', (17920, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.KNC3', (18432, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_KNC3', (18944, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZPPT005', (19456, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZPPT005', (19968, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T006A', (20480, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T006A', (20992, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.KNB1', (21504, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_KNB1', (22016, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.FR_PP_IN_SX_DTL_V', (22528, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_FR_PP_IN_SX_DTL_V', (23040, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.FR_QIS_JYWCL_GJ_V', (23552, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_FR_QIS_JYWCL_GJ_V', (24064, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.MLDOCCCS', (24576, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_MLDOCCCS', (25088, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ADR2', (25600, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ADR2', (26112, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TVKO', (26624, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TVKO', (27136, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TKA02', (27648, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TKA02', (28160, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.FAGLFLEXT', (28672, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_FAGLFLEXT', (29184, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T023T', (29696, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T023T', (30208, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_IQC', (30720, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_IQC', (31232, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.RBKP', (31744, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_RBKP', (32256, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.EQUI', (32768, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_EQUI', (33280, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZSDX4', (33792, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZSDX4', (34304, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_PAYEE_FORECURR_SUPPLIER', (34816, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_PAYEE_FORECURR_SUPPLIER', (35328, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T003T', (35840, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T003T', (36352, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTMDM_009', (36864, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTMDM_009', (37376, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TQ30T', (37888, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TQ30T', (38400, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.QAVE', (38912, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_QAVE', (39424, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.AFRU', (39936, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_AFRU', (40448, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZMMT007', (40960, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZMMT007', (41472, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.MAKT', (41984, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_MAKT', (42496, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.CSKT', (43008, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_CSKT', (43520, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ANLU', (44032, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ANLU', (44544, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTPP_015A', (45056, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTPP_015A', (45568, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTPP003', (46080, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTPP003', (46592, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI006', (47104, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTFI006', (47616, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T024', (48128, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T024', (48640, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.EQUP', (49152, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_EQUP', (49664, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI058', (50176, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTFI058', (50688, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.BUT0BK', (51200, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_BUT0BK', (51712, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.KNVV', (52224, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_KNVV', (52736, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_KTM_OUT_DETAIL', (53248, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_KTM_OUT_DETAIL', (53760, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T188T', (54272, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T188T', (54784, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.KONP', (55296, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_KONP', (55808, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTPP_015C', (56320, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTPP_015C', (56832, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ANLZ', (57344, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ANLZ', (57856, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZT_BC_HGZ', (58368, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI017', (58880, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTFI017', (59392, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZMMT0021', (59904, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZMMT0021', (60416, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_JEST_AUFK', (60928, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_JEST_AUFK', (61440, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZMMT0064', (61952, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZMMT0064', (62464, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI018', (62976, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTFI018', (63488, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI029', (64000, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTFI029', (64512, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_AFVV', (65024, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_AFVV', (65536, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZMARA', (66048, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZMARA', (66560, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.V_SALESPRICE_CUST', (67072, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_SALESPRICE_CUST', (67584, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_ZTQM011_ITEM', (68096, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_ZTQM011_ITEM', (68608, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MATERIAL_SOURCE', (69120, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_MATERIAL_SOURCE', (69632, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.KONH', (70144, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_KONH', (70656, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_VBFA', (71168, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_VBFA', (71680, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T074', (72192, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T074', (72704, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ADRC', (73216, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ADRC', (73728, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_AFKO_EX', (74240, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_AFKO', (74752, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.LAGP', (75264, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_LAGP', (75776, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.V_SALESPRICE_TYPE_VC', (76288, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_SALESPRICE_TYPE_VC', (76800, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T856T', (77312, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T856T', (77824, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_QMAT', (78336, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_QMAT', (78848, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TB002', (79360, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TB002', (79872, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTMDM_013_F2', (80384, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTMDM_013_F2', (80896, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TJ02T', (81408, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TJ02T', (81920, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.RESB', (82432, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_RESB', (82944, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T052U', (83456, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T052U', (83968, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTSD020', (84480, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTSD020', (84992, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.V_SALESPRICE_CUST_VC', (85504, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_SALESPRICE_CUST_VC', (86016, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TVAUT', (86528, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TVAUT', (87040, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.BUT000', (87552, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_BUT000', (88064, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTMM_BPM_005_N', (88576, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTMM_BPM_005_N', (89088, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.PLAS', (89600, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_PLAS', (90112, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZMMT012_ZC', (90624, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZMMT012_ZC', (91136, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_AFKO_TL', (91648, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_AFKO_TL', (92160, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_KNA1_S', (92672, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.LFB1', (93184, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_LFB1', (93696, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.EORD', (94208, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_EORD', (94720, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_APP_VEHICLE_SPECIFICATION', (95232, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_APP_VEHICLE_SPECIFICATION', (95744, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.LFA1', (96256, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_LFA1', (96768, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MBEW', (97280, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_MBEW', (97792, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.MCH1', (98304, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_MCH1', (98816, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TCDOB', (99328, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TCDOB', (99840, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.NSDM_V_MARD', (100352, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_NSDM_V_MARD', (100864, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_BP_BANKINFO', (101376, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_BP_BANKINFO', (101888, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTMM_BOM_IDNRK', (102400, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTMM_BOM_IDNRK', (102912, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MLDOCCCS_7D', (103424, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_MLDOCCCS_DELTA', (103936, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTSD0083', (104448, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTSD0083', (104960, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZMMT_AGE', (105472, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZMMT_AGE', (105984, 51)
'get_table_id:SAP_DATA.CFD.CFTEST.ODS_SAP_D_V_AFKO_TL', (106496, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MATDOC', (107008, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_MATDOC', (107520, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.OBJK', (108032, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_INNERORDER', (108544, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_INNERORDER', (109056, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_ZMMT003', (109568, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_ZMMT003', (110080, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTCGPL', (110592, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTCGPL', (111104, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MATERIAL_PURCHASE_INFO', (111616, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DWD_MATERIAL_PURCHASE_INFO_D', (112128, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TVKGR', (112640, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TVKGR', (113152, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MK_COSTCENTER', (113664, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_MK_SAP_COSTCENTER', (114176, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_VBRK', (114688, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_VBRK', (115200, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_CUSTOMER_ADDRESS', (115712, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_CUSTOMER_ADDRESS', (116224, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TKKH2', (116736, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TKKH2', (117248, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_VEHICLE_CONFIG_INFO', (117760, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_VEHICLE_CONFIG_INFO', (118272, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T030', (118784, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T030', (119296, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_LASTPURCHASEPRICE', (119808, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_LASTPURCHASEPRICE', (120320, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_SALESPRICE_TYPE', (120832, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_SALESPRICE_TYPE', (121344, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_SALESPRICE_CUST', (121856, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_CDPOS', (122368, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_CDPOS_I', (122880, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_ZMMT_BC_18', (123392, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TBOX_PROD_ORDER_REL_MXG', (123904, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI093B', (124416, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_FICO_ZTFI093B', (124928, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZMDM_TJ', (125440, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZMDM_TJ', (125952, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_OBJK', (126464, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.CSKU', (126976, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_CSKU', (127488, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.A901', (128000, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_A901', (128512, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI007', (129024, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTFI007', (129536, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TCURR', (130048, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T189T', (130560, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T189T', (131072, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_PURCHASEPRICE_DTL', (131584, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_PURCHASEPRICE_DTL', (132096, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_CUSTOMER_BASE', (132608, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_CUSTOMER_BASE', (133120, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_VBAK', (133632, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.KEKO', (134144, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_KEKO', (134656, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.DFKKBPTAXNUM', (135168, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_DFKKBPTAXNUM', (135680, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_RSEG', (136192, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_RSEG', (136704, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T001K', (137216, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T001K', (137728, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.KNC1', (138240, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_KNC1', (138752, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.IBINVALUES', (139264, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_IBINVALUES', (139776, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.PLAF', (140288, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_PLAF', (140800, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_PRODUCT_MATERIAL_REL', (141312, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZMDM_CJX', (141824, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZMDM_CJX', (142336, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_AUFK_EX', (142848, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_AUFK', (143360, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZMMT073', (143872, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZMMT073', (144384, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T024D', (144896, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T024D', (145408, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MATERIAL_INVENTORY', (145920, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_MATERIAL_INVENTORY', (146432, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_DMS_SJBG_SXRQ', (146944, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_DMS_SJBG_SXRQ', (147456, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_OUTBUY_PROD_ORDER_FEED_DTL', (147968, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_OUTBUY_PROD_ORDER_FEED_DTL', (148480, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_LIKP', (148992, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_LIKP', (149504, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_SUPPLIER_PURCHASE', (150016, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_SUPPLIER_PURCHASE', (150528, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZSDX5', (151040, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZSDX5', (151552, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.CABN', (152064, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_CABN', (152576, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T001W', (153088, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T001W', (153600, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.A017', (154112, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_A017', (154624, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.DD07T', (155136, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_DD07T', (155648, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.CSKS', (156160, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_CSKS', (156672, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.MARA', (157184, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_MARA', (157696, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI013D', (158208, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTFI013D', (158720, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_VBAP', (159232, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_VBAP', (159744, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.CEPC', (160256, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_CEPC', (160768, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.VBPA', (161280, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_VBPA', (161792, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TVTWT', (162304, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TVTWT', (162816, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZMMT_AGE_FR', (163328, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.CDHDR', (163840, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_CDHDR_I', (164352, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_AFPO', (164864, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_AFPO', (165376, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.QALS', (165888, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_QALS', (166400, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.EKKN', (166912, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_EKKN', (167424, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_EKKO', (167936, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_EKKO', (168448, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TVKBT', (168960, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TVKBT', (169472, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI010', (169984, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTFI010', (170496, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TVBVK', (171008, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TVBVK', (171520, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MATERIAL_FACTORY_BOM', (172032, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_MATERIAL_FACTORY_BOM', (172544, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_ENGINE_YEAR_VC', (173056, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_ENGINE_YEAR_VC', (173568, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ANLA', (174080, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ANLA', (174592, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTLONGTEXT_MARA', (175104, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTLONGTEXT_MARA', (175616, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_CUSTOMER', (176128, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_CUSTOMER', (176640, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_LAST_PURC_PRICE_ALL', (177152, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_LAST_PURC_PRICE_ALL', (177664, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_ADS_APP_MIN_OUTSTOCK', (178176, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_ADS_APP_MIN_OUTSTOCK', (178688, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_VBAK', (179200, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_CDHDR', (179712, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MARC', (180224, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_MARC', (180736, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTQM009', (181248, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTQM009', (181760, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.SKAT', (182272, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_SKAT', (182784, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZMM001', (183296, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZMM001', (183808, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTPUB_ZFI027', (184320, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTPUB_ZFI027', (184832, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T134T', (185344, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T134T', (185856, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_CUSTOMER_RATIO', (186368, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_CUSTOMER_RATIO', (186880, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI013', (187392, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTFI013', (187904, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.V_SALESPRICE_COMPANY', (188416, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_SALESPRICE_COMPANY', (188928, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TVSBT', (189440, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TVSBT', (189952, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T006', (190464, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T006', (190976, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.MLDOC', (191488, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_MLDOC', (192000, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.SKB1', (192512, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_SKB1', (193024, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TCURX', (193536, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TCURX', (194048, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TFKBT', (194560, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TFKBT', (195072, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_BKPF', (195584, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_BKPF', (196096, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.FR_PP_IN_DTL_V', (196608, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_FR_PP_IN_DTL_V', (197120, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_LIPS', (197632, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_LIPS', (198144, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T005U', (198656, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T005U', (199168, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.CE1CFMT', (199680, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_CE1CFMT', (200192, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.IBIN', (200704, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_IBIN', (201216, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.CKMLHD', (201728, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_CKMLHD', (202240, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.USR21', (202752, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_USR21', (203264, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ANKT', (203776, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ANKT', (204288, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_SALESPRICE_COMPANY', (204800, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_ACDOCA', (205312, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_ACDOCA', (205824, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T156T', (206336, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T156T', (206848, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_PURCHASEPRICE', (207360, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_PURCHASEPRICE', (207872, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_BSEG', (208384, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_BSEG', (208896, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T006D', (209408, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T006D', (209920, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.LFM1', (210432, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_LFM1', (210944, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TGSBT', (211456, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TGSBT', (211968, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MATERIAL_FACTORY', (212480, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_MATERIAL_FACTORY', (212992, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ADR6', (213504, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ADR6', (214016, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_EKET', (214528, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_EKET', (215040, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ACESOBJ_ASSGMT', (215552, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ACESOBJ_ASSGMT', (216064, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T301T', (216576, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T301T', (217088, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TFKTAXNUMTYPE_T', (217600, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TFKTAXNUMTYPE_T', (218112, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_CDPOS', (218624, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_CUSTOMER_STORE', (219136, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_CUSTOMER_STORE', (219648, 51)
'get_table_id:SAP_DATA.CFD.CFTEST.ODS_CEPC', (220160, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTCO001', (220672, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTCO001', (221184, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_SUPPLIER_COMPANY', (221696, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_SUPPLIER_COMPANY', (222208, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TCURT', (222720, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TCURT', (223232, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZMIGO_BADI_SRM01', (223744, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZMIGO_BADI_SRM01', (224256, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.IBSYMBOL', (224768, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_IBSYMBOL', (225280, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T001', (225792, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T001', (226304, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_VBRP', (226816, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_VBRP', (227328, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.SKA1', (227840, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_SKA1', (228352, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI009', (228864, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTFI009', (229376, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T006T', (229888, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T006T', (230400, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_JEST_VBAK', (230912, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_JEST_VBAK', (231424, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTLONGTEXT_SALES', (231936, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTLONGTEXT_SALES', (232448, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MATERIAL', (232960, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_MATERIAL', (233472, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T005T', (233984, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T005T', (234496, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T053S', (235008, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T053S', (235520, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.LFC3', (236032, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_LFC3', (236544, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.SER03', (237056, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_SER03', (237568, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.CEPCT', (238080, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_CEPCT', (238592, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T024F', (239104, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T024F', (239616, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.V_SALESPRICE_TYPE', (240128, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T024E', (240640, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T024E', (241152, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.PLPO', (241664, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_PLPO', (242176, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TABWT', (242688, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TABWT', (243200, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TCURF', (243712, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TCURF', (244224, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZPPTCO02', (244736, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZPPTCO02', (245248, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_EKPO', (245760, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_EKPO', (246272, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TCURR', (246784, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.T002', (247296, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_T002', (247808, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI004', (248320, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTFI004', (248832, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_SUPPLIER_BASE', (249344, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_SUPPLIER_BASE', (249856, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.FRM_FICO_XM_YS_DTL_V', (250368, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_FRM_FICO_XM_YS_DTL_V', (250880, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_AFVC', (251392, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_AFVC', (251904, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_ZMMT_BC_18', (252416, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_VBKD', (252928, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_VBKD', (253440, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_FOREIGN_PART_OUT_SUM', (253952, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_FOREIGN_PART_OUT_SUM', (254464, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.KNVP', (254976, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_KNVP', (255488, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.CRCO', (256000, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_CRCO', (256512, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_SALESPRICE_CUST_VC', (257024, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_CUSTOMER_PARTNER', (257536, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TSPAT', (258048, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TSPAT', (258560, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_ZTQM011_HEAD', (259072, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_ZTQM011_HEAD', (259584, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_ZMMT_BC_38', (260096, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_ZMMT_BC_38', (260608, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_STANDARD_VEHICLE_CONFIG_INFO', (261120, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_STANDARD_VEHICLE_CONFIG_INFO', (261632, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.SER01', (262144, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_SER01', (262656, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_PAYEE_SUPPLIER', (263168, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_PAYEE_SUPPLIER', (263680, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTQM011_JYY', (264192, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_ZTQM011_JYY', (264704, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.EQUK', (265216, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_EQUK', (265728, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.TVKOT', (266240, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_TVKOT', (266752, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MATE_WERK_TYPE', (267264, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_MATE_WERK_TYPE', (267776, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_V_CUSTOMER_PARTNER', (268288, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_SALESPRICE_TYPE_VC', (268800, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.QPCT', (269312, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.ODS_QPCT', (269824, 51)
'get_table_id:bpm.CFBPMDB.DBO.V_CF_GY_SBJY_NEW', (270336, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_GY_SBJY_NEW', (270848, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_RDC_NEWPRODUCTPROJECTAPPROVAL_P', (271360, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_RDC_NEWPRODUCTPROJECTAPPROVAL_P', (271872, 51)
'get_table_id:bpm.CKMBPMDB.DBO.BPMINSTTASKS', (272384, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.TMP_KTM_BPMINSTTASKS', (272896, 51)
'get_table_id:bpm.MXBPMDB.DBO.BPMSYSOUS', (273408, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_MX_BPMSYSOUS', (273920, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_VQA_COMPONENTFEEDQUALITYPROBLEMTRACKING_P', (274432, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_COMPONENTFEEDQUALITYPROBLEMTRACKING_P', (274944, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZLL_VEHICLESTATE_CONFIGITEM', (275456, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZLL_VEHICLESTATE_CONFIGITEM', (275968, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_RDC_SIPU', (276480, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_RDC_SIPU', (276992, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_GY_SBJY_P_D_BM_ZZ', (277504, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_GY_SBJY_P_D_BM_ZZ', (278016, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZL_COMPONENTVERIFICATE_P', (278528, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_COMPONENTVERIFICATE_P', (279040, 51)
'get_table_id:bpm.BPMDB.DBO.BPMSYSOUS', (279552, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_BPMSYSOUS', (280064, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VOA_DESIGNCHANGEMANAGEMENT_P_D_SZBG', (280576, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VOA_DESIGNCHANGEMANAGEMENT_P_D_SZBG', (281088, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_DMS_SBSP_CLAIM_GN_P_D', (281600, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_DMS_SBSP_CLAIM_GN_P_D', (282112, 51)
'get_table_id:bpm.CFBPMDB.DBO.V_CF_DMS_SBSP_CLAIM_GN_P', (282624, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_DMS_SBSP_CLAIM_GN_P', (283136, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_DMS_SBSP_CLAIM_GN_P_D_ITEM', (283648, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_DMS_SBSP_CLAIM_GN_P_D_ITEM', (284160, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_DMS_SBSP_CLAIM_GW_P_D', (284672, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_DMS_SBSP_CLAIM_GW_P_D', (285184, 51)
'get_table_id:bpm.BPMDB.DBO.V_DMS_XSDDPS', (285696, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_DMS_XSDDPS', (286208, 51)
'get_table_id:bpm.BPMDB.DBO.V_CF_FIN_BANKRECEIPT_UNDECLAREBALANCE', (286720, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_FIN_BANKRECEIPT_UNDECLAREBALANCE', (287232, 51)
'get_table_id:bpm.CQBPMDB.DBO.BPMINSTPROCSTEPS', (287744, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CQ_BPMINSTPROCSTEPS', (288256, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_VEN_BD_PURCHASECATEGORYDIVISIONLIBRARY', (288768, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VEN_BD_PURCHASECATEGORYDIVISIONLIBRARY', (289280, 51)
'get_table_id:bpm.CFBPMDB.DBO.V_CF_RDC_OTAVERSIONUPGRADECONFIRMATION_P', (289792, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_RDC_OTAVERSIONUPGRADECONFIRMATION_P', (290304, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SCA_ZEEHODEALERSHOPCHECKANDACCEPT_P_DJSWL', (290816, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPCHECKANDACCEPT_P_DJSWL', (291328, 51)
'get_table_id:bpm.MXBPMDATA.DBO.MX_VQA_WASTEQUANTITY', (291840, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_MX_VQA_WASTEQUANTITY', (292352, 51)
'get_table_id:bpm.MXBPMDB.DBO.BPMSYSUSERS', (292864, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_MX_BPMSYSUSERS', (293376, 51)
'get_table_id:bpm.CFBPMNEW.DBO.V_CF_DEALER_TRY', (293888, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_DEALER_TRY_S', (294400, 51)
'get_table_id:bpm.CFMOTO.DBO.V_ECU_DETAIL', (294912, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_VQA_PRODUCTIONSITESTOPLINEFEEDBACK_P', (295424, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_PRODUCTIONSITESTOPLINEFEEDBACK_P', (295936, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VOA_DESIGNCHANGEMANAGEMENT_P_D_SJBGSL', (296448, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VOA_DESIGNCHANGEMANAGEMENT_P_D_SJBGSL', (296960, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SEP_SAFETYRECTIFICATIONMULTI_P', (297472, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SEP_SAFETYRECTIFICATIONMULTI_P', (297984, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SAP_SD_XSDDBG', (298496, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SAP_SD_XSDDBG', (299008, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_GY_SBJY_P', (299520, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_GY_SBJY_P', (300032, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SCA_ZEEHODEALERSHOPMATERALAPPLY_P_D1', (300544, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPMATERALAPPLY_P_D1', (301056, 51)
'get_table_id:bpm.BPMDB.DBO.V_CF_ZL_SCGZTZXZ_V', (301568, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_ZL_SCGZTZXZ_V_BI', (302080, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VQA_DESIGNCHANGEDEVELOPMENT_P', (302592, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_DESIGNCHANGEDEVELOPMENT_P', (303104, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_BANKRECEIPT_P_D', (303616, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_BANKRECEIPT_P_D', (304128, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VOA_DESIGNCHANGEMANAGEMENT_P_D_SJBGLL', (304640, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VOA_DESIGNCHANGEMANAGEMENT_P_D_SJBGLL', (305152, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ZL_SPOTQUALITYDISPOSE_P', (305664, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_SPOTQUALITYDISPOSE_P_NEW', (306176, 51)
'get_table_id:bpm.BPMDB.DBO.V_CF_HR_LEFTOVER_PS', (306688, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_HR_LEFTOVER_PS', (307200, 51)
'get_table_id:bpm.BPMDB.DBO.CF_EAM_ASSETSFINISHACCEPTANCE', (307712, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_EAM_ASSETSFINISHACCEPTANCE', (308224, 51)
'get_table_id:bpm.CFBPMNEW.DBO.V_CF_KU_DEALER_STORE', (308736, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_KU_DEALER_STORE_S', (309248, 51)
'get_table_id:bpm.MXBPMDATA.DBO.MX_PM_CFMKDREISSUE', (309760, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_MX_PM_CFMKDREISSUE', (310272, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_RPLBJDY_1', (310784, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_RPLBJDY_1', (311296, 51)
'get_table_id:bpm.MXBPMDB.DBO.BPMINSTTASKS', (311808, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_MX_BPMINSTTASKS', (312320, 51)
'get_table_id:bpm.BPMDB.DBO.V_MK_SBTJ', (312832, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_MK_SBTJ', (313344, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ADM_FREEDOCUMENTENDORSEMENT_P_D2', (313856, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ADM_FREEDOCUMENTENDORSEMENT_P_D2', (314368, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_BANKRECEIPTKU_P', (314880, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_BANKRECEIPTKU_P', (315392, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ZL_SPOTQUALITYDISPOSE_WLINFO', (315904, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_SPOTQUALITYDISPOSE_WLINFO', (316416, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_DISPOSE', (316928, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_DISPOSE', (317440, 51)
'get_table_id:bpm.BPMDB.DBO.V_CF_HR_LEFTOVER_P', (317952, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_HR_LEFTOVER_P', (318464, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ZL_PLZLSG_2', (318976, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_PLZLSG_2', (319488, 51)
'get_table_id:bpm.BPMDB.DBO.V_CF_SALE_OPENACCOUNT', (320000, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_SALE_OPENACCOUNT', (320512, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZL_GYTEMPORARY_P', (321024, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_GYTEMPORARY_P', (321536, 51)
'get_table_id:bpm.BPMDB.DBO.CF_SALE_RETURNOFFULLVEHICLEORDERSONLINE', (322048, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SALE_RETURNOFFULLVEHICLEORDERSONLINE', (322560, 51)
'get_table_id:bpm.CFMOTO.DBO.T_VEHICLEINFO_FORCARONLINE', (323072, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_T_VEHICLEINFO_FORCARONLINE', (323584, 51)
'get_table_id:bpm.CFDW.DBO.DWD_PURCH_H_CURPURPRICE', (324096, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.TMP_DWD_PURCH_H_CURPURPRICE', (324608, 51)
'get_table_id:bpm.BPMDB.DBO.V_CF_FIN_BANKRECEIPT_BALANCE', (325120, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_FIN_BANKRECEIPT_BALANCE', (325632, 51)
'get_table_id:bpm.BPMDB.DBO.V_CF_GLFJL', (326144, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_GLFJL', (326656, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_SCA_REPAIRROOM_DISIGN_P', (327168, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SCA_REPAIRROOM_DISIGN_P', (327680, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ZL_PLZLSG_1', (328192, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_PLZLSG_1', (328704, 51)
'get_table_id:bpm.KTMBPMDB.DBO.KTM_SAP_MDM_MATERIALCOLLAR_P_D', (329216, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_KTM_SAP_MDM_MATERIALCOLLAR_P_D', (329728, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_TRIALVEHICLEMATERIALPURCHASEAPPLICATION_P_D', (330240, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASEAPPLICATION_P_D', (330752, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_RPHTSP_1', (331264, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_RPHTSP_1', (331776, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SCA_ZEEHODEALERSHOPKU', (332288, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPKU', (332800, 51)
'get_table_id:bpm.BPMDB.DBO.U_ZLL_WASTEQUANTITY', (333312, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_U_ZLL_WASTEQUANTITY', (333824, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_GY_SBJY', (334336, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_TRIALVEHICLEMATERIALPURCHASE_P_D', (334848, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASE_P_D', (335360, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZLL_VEHICLESTATE_CONFIRM', (335872, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZLL_VEHICLESTATE_CONFIRM', (336384, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_FIN_BUDGETADJUST_1', (336896, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_BUDGETADJUST_1', (337408, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VQA_DESIGNCHANGESTRATEGY_P_D', (337920, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_DESIGNCHANGESTRATEGY_P_D', (338432, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VQA_ORDERREVIEW_P_D_1', (338944, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_ORDERREVIEW_P_D_1', (339456, 51)
'get_table_id:bpm.BPMDB.DBO.V_FR_PP_WLLY', (339968, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_FR_PP_WLLY', (340480, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_IT_REQUIREMENTSFORSAP_P', (340992, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_IT_REQUIREMENTSFORSAP_P', (341504, 51)
'get_table_id:bpm.CFBPMDB.DBO.V_DMS_GWKHTK', (342016, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_DMS_GWKHTK', (342528, 51)
'get_table_id:bpm.BPMDB.DBO.V_CONTRACT_ENGINE_YEAR', (343040, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CONTRACT_ENGINE_YEAR', (343552, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_DEALER_STORE_CONTACT_TYPE', (344064, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_DEALER_STORE_CONTACT_TYPE', (344576, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_BANKRECEIPT_P', (345088, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_BANKRECEIPT_P', (345600, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_NEWPRODUCTDEVELOPMENTAGREEMENT_P_D_BC', (346112, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_NEWPRODUCTDEVELOPMENTAGREEMENT_P_D_BC', (346624, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_SELFTOOL_D_P', (347136, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SALE_SELFTOOL_D_P', (347648, 51)
'get_table_id:bpm.CFBPMDB.DBO.V_DMS_SBJY', (348160, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_DMS_SBJY', (348672, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_JS_XPSZCJH_1', (349184, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_JS_XPSZCJH_1', (349696, 51)
'get_table_id:bpm.MXBPMDATA.DBO.V_BPMINSTPROCSTEPS', (350208, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_MX_V_BPMINSTPROCSTEPS', (350720, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_DMS_SBSP_CLAIM_GW_P', (351232, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_DMS_SBSP_CLAIM_GW_P', (351744, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_GY_SBJY_P_D', (352256, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_GY_SBJY_P_D', (352768, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_DECLARATE_P_D', (353280, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_DECLARATE_P_D', (353792, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_VQA_GENERATIVEDISPATCH_P', (354304, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_GENERATIVEDISPATCH_P', (354816, 51)
'get_table_id:bpm.CFBPMNEW.DBO.V_CF_KU_DEALER_STORE_CONTACT', (355328, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_KU_DEALER_STORE_CONTACT_S', (355840, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_NEWPRODUCTDEVELOPMENTAGREEMENT_P', (356352, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_NEWPRODUCTDEVELOPMENTAGREEMENT_P', (356864, 51)
'get_table_id:bpm.BPMDB.DBO.CF_SUP_SUPPLIERMOULD_P', (357376, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_SUPPLIERMOULD_P', (357888, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SAP_MDM_WLXZXG_MX', (358400, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SAP_MDM_WLXZXG_MX', (358912, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_GY_SBJY_S', (359424, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZL_GYTEMPORARY_P_D', (359936, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_GYTEMPORARY_P_D', (360448, 51)
'get_table_id:bpm.CFBPMDB.DBO.V_BPMINSTTASKS', (360960, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_BPMINSTTASKS', (361472, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_PM_EQUIPMENTTRAINING', (361984, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_PM_EQUIPMENTTRAINING', (362496, 51)
'get_table_id:bpm.BPMDB.DBO.CF_SUP_SUPPLIERMOULD_P_D', (363008, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_SUPPLIERMOULD_P_D', (363520, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_VQA_INSPECTIONEXEMPTIONSUPPLIER_P', (364032, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_INSPECTIONEXEMPTIONSUPPLIER_P', (364544, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZL_FEEDBACK_P', (365056, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_FEEDBACK_P', (365568, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZL_SPOTQUALITYDISPOSE_P', (366080, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_SPOTQUALITYDISPOSE_P', (366592, 51)
'get_table_id:bpm.CFMOTO.DBO.T_ERP_ICITEM', (367104, 51)
'get_table_id:SAP_DATA.CFD.CFTEST.ODS_CF_D_T_ERP_ICITEM', (367616, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VQA_DESIGNCHANGESTRATEGY_P', (368128, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_DESIGNCHANGESTRATEGY_P', (368640, 51)
'get_table_id:bpm.CFBPMDB.DBO.V_BPMINSTPROCSTEPS', (369152, 51)
'get_table_id:bpm.BPMDB.DBO.CF_SALE_RFVORDERSONLINE_1', (369664, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SALE_RFVORDERSONLINE_1', (370176, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_FIN_WGCCGSQ', (370688, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_WGCCGSQ', (371200, 51)
'get_table_id:bpm.CFBPMDB.DBO.V_DMS_GNKHSP', (371712, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_DMS_GNKHSP', (372224, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SAP_FICO_FKSQ_LX', (372736, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SAP_FICO_FKSQ_LX', (373248, 51)
'get_table_id:bpm.CFMOTO.DBO.T_VEHICLEINFO', (373760, 51)
'get_table_id:SAP_DATA.CFD.CFK3.ODS_T_VEHICLEINFO', (374272, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VQA_DESIGNCHANGEDEVELOPMENT_P_D', (374784, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_DESIGNCHANGEDEVELOPMENT_P_D', (375296, 51)
'get_table_id:bpm.BPMDB.DBO.V_HR_YLZ', (375808, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_HR_YLZ', (376320, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_BPMINSTPROCSTEPS', (376832, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SAP_FICO_FKSQ_LX_WB_GJSZCODEMENU', (377344, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SAP_FICO_FKSQ_LX_WB_GJSZCODEMENU', (377856, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZLL_VEHICLESTATE_STATEITEM', (378368, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZLL_VEHICLESTATE_STATEITEM', (378880, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_SCA_REPAIRROOM_CHECKANDACCEPT_P', (379392, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SCA_REPAIRROOM_CHECKANDACCEPT_P', (379904, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_NEWPRODUCTDEVELOPMENTAGREEMENT_P_D_BC_C', (380416, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_NEWPRODUCTDEVELOPMENTAGREEMENT_P_D_BC_C', (380928, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_SCA_REPAIRROOM_CONSTRUCTION_P', (381440, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SCA_REPAIRROOM_CONSTRUCTION_P', (381952, 51)
'get_table_id:bpm.BPMDB.DBO.CF_CW_BGINVOICE', (382464, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_CW_BGINVOICE_S', (382976, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VQA_ORDERREVIEW_P_D_0', (383488, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_ORDERREVIEW_P_D_0', (384000, 51)
'get_table_id:bpm.BPMDB.DBO.CF_SUP_SUPPLIERDEVELOP_BASEDATA', (384512, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_SUPPLIERDEVELOP_BASEDATA', (385024, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_CONTRACTAPPROVALAPPLICATION_P', (385536, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_CONTRACTAPPROVALAPPLICATION_P', (386048, 51)
'get_table_id:bpm.CFBPMDB.DBO.V_CF_RDC_NEWPRODUCTPROJECTAPPROVAL_P', (386560, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_RDC_NEWPRODUCTPROJECTAPPROVAL_P', (387072, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VOA_DESIGNCHANGEMANAGEMENT_P_D', (387584, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VOA_DESIGNCHANGEMANAGEMENT_P_D', (388096, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_JS_PROJECTMEMBERLIST', (388608, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_JS_PROJECTMEMBERLIST', (389120, 51)
'get_table_id:bpm.BPMDB.DBO.V_MK_GXWLLY', (389632, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_MK_GXWLLY', (390144, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_BANKADJUST_P_D', (390656, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_BANKADJUST_P_D', (391168, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_TRIALVEHICLEMATERIALPURCHASEAPPLICATION_P', (391680, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASEAPPLICATION_P', (392192, 51)
'get_table_id:bpm.KTMBPMDB.DBO.V_BPMINSTTASKS', (392704, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_KTM_V_BPMINSTTASKS', (393216, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VOA_DESIGNCHANGEMANAGEMENT_P_D_SJBGFDJ', (393728, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VOA_DESIGNCHANGEMANAGEMENT_P_D_SJBGFDJ', (394240, 51)
'get_table_id:bpm.BPMDB.DBO.V_DMS_SJBG', (394752, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_DMS_SJBG', (395264, 51)
'get_table_id:bpm.MXBPMDATA.DBO.MX_PM_CFMKDREISSUE_D', (395776, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_MX_PM_CFMKDREISSUE_D', (396288, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_FIN_FXSCPZY', (396800, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_FXSCPZY', (397312, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_RPLBJDY', (397824, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_RPLBJDY', (398336, 51)
'get_table_id:bpm.CFMOTO.DBO.T_SYNC_VEHICLECERTIFICATION', (398848, 51)
'get_table_id:SAP_DATA.CFD.CFK3.ODS_T_SYNC_VEHICLECERTIFICATION', (399360, 51)
'get_table_id:bpm.CFBPMDB.DBO.V_DMS_GNKHFKSQ', (399872, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_DMS_GNKHFKSQ', (400384, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ADM_FREEDOCUMENTENDORSEMENT_P_D1', (400896, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ADM_FREEDOCUMENTENDORSEMENT_P_D1', (401408, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ADM_FREEDOCUMENTENDORSEMENT_P', (401920, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ADM_FREEDOCUMENTENDORSEMENT_P', (402432, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_GY_SUPPLIERPROCUREMENTPRICEMATCHING', (402944, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_GY_SUPPLIERPROCUREMENTPRICEMATCHING', (403456, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_RDC_NEWPRODUCTMOULDINSTRUCTION_P', (403968, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_RDC_NEWPRODUCTMOULDINSTRUCTION_P', (404480, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_RPLBJDD', (404992, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_RPLBJDD', (405504, 51)
'get_table_id:bpm.BPMDB.DBO.U_ZLL_WASTEQUANTITYENTRY', (406016, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_U_ZLL_WASTEQUANTITYENTRY', (406528, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SCA_ZEEHODEALERNEWACCOUNTAPPLICATION_P', (407040, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERNEWACCOUNTAPPLICATION_P', (407552, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SEP_SAFETYRECTIFICATIONMULTI_P_D', (408064, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SEP_SAFETYRECTIFICATIONMULTI_P_D', (408576, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_DMS_SBSP_CLAIM_GN_P', (409088, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_DMS_SBSP_CLAIM_GN_P', (409600, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_SH_HTSPSQ', (410112, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SH_HTSPSQ', (410624, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_VQA_SBSCRAPDISPOSE_P', (411136, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_SBSCRAPDISPOSE_P', (411648, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_RDC_SIPU_SEND', (412160, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_RDC_SIPU_SEND', (412672, 51)
'get_table_id:bpm.MXBPMDATA.DBO.MX_VQA_VEHICLEEVALUATIONINFORMATIONCFMCFPA_P', (413184, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_MX_VQA_VEHICLEEVALUATIONINFORMATIONCFMCFPA_P', (413696, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_FIN_WGCCGSQ_1', (414208, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_WGCCGSQ_1', (414720, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_JS_XPSZCJH', (415232, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_JS_XPSZCJH', (415744, 51)
'get_table_id:bpm.BPMDB.DBO.V_CF_HR_CHANGEDEPTPO_PS', (416256, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_HR_CHANGEDEPTPO_PS', (416768, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_TRIALVEHICLEMATERIALPURCHASEORDER_P', (417280, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASEORDER_P', (417792, 51)
'get_table_id:bpm.MXBPMDB.DBO.BPMINSTPROCSTEPS', (418304, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.TMP_MX_BPMINSTPROCSTEPS', (418816, 51)
'get_table_id:bpm.CFBPMDB.DBO.V_DMS_GWKHSP', (419328, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_DMS_GWKHSP', (419840, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.TMP_MX_BPMINSTTASKS', (420352, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZLL_VEHICLESTATE_CONFIGTYPE', (420864, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZLL_VEHICLESTATE_CONFIGTYPE', (421376, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ADM_FREEDOCUMENTENDORSEMENT_P_D', (421888, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ADM_FREEDOCUMENTENDORSEMENT_P_D', (422400, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_RPLBJDD_3', (422912, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_RPLBJDD_3', (423424, 51)
'get_table_id:bpm.MXBPMDATA.DBO.MX_VQA_WASTEQUANTITYENTRY', (423936, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_MX_VQA_WASTEQUANTITYENTRY', (424448, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_TRIALVEHICLEMATERIALPURCHASEORDER_P_D', (424960, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASEORDER_P_D', (425472, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SCA_ZEEHODEALERSHOPCHECKANDACCEPT_P', (425984, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPCHECKANDACCEPT_P', (426496, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ZL_ORIGINALPARTS_P', (427008, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_ORIGINALPARTS_P', (427520, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VOA_DESIGNCHANGEMANAGEMENT_P', (428032, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VOA_DESIGNCHANGEMANAGEMENT_P', (428544, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_SBSP_DRAWMONEY', (429056, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SBSP_DRAWMONEY', (429568, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ZL_PLZLSG', (430080, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_PLZLSG', (430592, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SCA_ZEEHODEALERSHOPMATERALAPPLY_P', (431104, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPMATERALAPPLY_P', (431616, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SAP_SD_XSDDBG_1', (432128, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SAP_SD_XSDDBG_1', (432640, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VQA_ORDERREVIEW_P', (433152, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_ORDERREVIEW_P', (433664, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VQA_MATERIALCOLLAR_P_D', (434176, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_MATERIALCOLLAR_P_D', (434688, 51)
'get_table_id:bpm.MXBPMDATA.DBO.V_BPMINSTTASKS', (435200, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_MX_V_BPMINSTTASKS', (435712, 51)
'get_table_id:bpm.CFDW.DBO.T_SAP_PAYMENTNATURE', (436224, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_T_SAP_PAYMENTNATURE', (436736, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_BANKADJUST_P', (437248, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_BANKADJUST_P', (437760, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_SELFTOOL', (438272, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SALE_SELFTOOL', (438784, 51)
'get_table_id:bpm.BPMDB.DBO.BPMINSTTASKS', (439296, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.TMP_BPMINSTTASKS', (439808, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SCA_ZEEHODEALERKU', (440320, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERKU', (440832, 51)
'get_table_id:bpm.BPMDB.DBO.BPMSYSUSERS', (441344, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_BPMSYSUSERS', (441856, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_CW_BGINVOICE', (442368, 51)
'get_table_id:bpm.CFBPMNEW.DBO.V_CF_KU_DEALER_STORE_TRY', (442880, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_KU_DEALER_STORE_TRY_S', (443392, 51)
'get_table_id:bpm.BPMDB.DBO.V_MK_WLLY', (443904, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_MK_WLLY', (444416, 51)
'get_table_id:bpm.BPMDB.DBO.V_MK_SDLDQR', (444928, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_MK_SDLDQR', (445440, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZL_GYTEMPORARY_P_D_0', (445952, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_GYTEMPORARY_P_D_0', (446464, 51)
'get_table_id:bpm.KTMBPMDB.DBO.KTM_SAP_MDM_MATERIALCOLLAR_P', (446976, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_KTM_SAP_MDM_MATERIALCOLLAR_P', (447488, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_GY_SBJY_P_D_BM', (448000, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_GY_SBJY_P_D_BM', (448512, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_GY_SUPPLIERPROCUREMENTPRICEMATCHING_D', (449024, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_GY_SUPPLIERPROCUREMENTPRICEMATCHING_D', (449536, 51)
'get_table_id:bpm.BPMDB.DBO.BPMINSTPROCSTEPS', (450048, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.TMP_BPMINSTPROCSTEPS', (450560, 51)
'get_table_id:bpm.CFBPMDB.DBO.V_CF_GY_SBJY', (451072, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_GY_SBJY_OLD', (451584, 51)
'get_table_id:bpm.CFBPMNEW.DBO.V_CF_HR_PERFORMANCEAPPRAISAL_PS', (452096, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_HR_PERFORMANCEAPPRAISAL_PS', (452608, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ZL_PLZLSG_3', (453120, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_PLZLSG_3', (453632, 51)
'get_table_id:bpm.BPMDB.DBO.CF_IT_REQUIREMENTSFORBPM_P', (454144, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_IT_REQUIREMENTSFORBPM_P', (454656, 51)
'get_table_id:bpm.MXBPMDATA.DBO.MX_ADM_DOCUMENTENDORSEMENT_P', (455168, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_MX_ADM_DOCUMENTENDORSEMENT_P', (455680, 51)
'get_table_id:bpm.CFBPMNEW.DBO.T_CF_RDC_PROJECTSPECIFICPARTSLIST', (456192, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_T_CF_RDC_PROJECTSPECIFICPARTSLIST', (456704, 51)
'get_table_id:bpm.MXBPMDB.DBO.BPMSYSOUMEMBERS', (457216, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_MX_BPMSYSOUMEMBERS', (457728, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_SELFTOOL_D', (458240, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SALE_SELFTOOL_D', (458752, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_DECLARATE_P', (459264, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_DECLARATE_P', (459776, 51)
'get_table_id:bpm.BPMDB.DBO.BPMSYSOUMEMBERS', (460288, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_BPMSYSOUMEMBERS', (460800, 51)
'get_table_id:bpm.BPMDB.DBO.CF_VQA_MATERIALCOLLAR_P', (461312, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_MATERIALCOLLAR_P', (461824, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZL_CONFIRMATIONSWITCHINGINFORMATION_P', (462336, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_CONFIRMATIONSWITCHINGINFORMATION_P', (462848, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SCA_ZEEHODEALERSHOPCONSTRUCTION_P', (463360, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPCONSTRUCTION_P', (463872, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_RDC_NEWPRODUCTMOULDINSTRUCTION_P_D', (464384, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_RDC_NEWPRODUCTMOULDINSTRUCTION_P_D', (464896, 51)
'get_table_id:bpm.CQBPMDB.DBO.BPMINSTTASKS', (465408, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CQ_BPMINSTTASKS', (465920, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_RPHTSP', (466432, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_RPHTSP', (466944, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_FIN_BUDGETADJUST', (467456, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_BUDGETADJUST', (467968, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZLL_VEHICLESTATE_CONFIRM_STATE', (468480, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZLL_VEHICLESTATE_CONFIRM_STATE', (468992, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_TRIALVEHICLEMATERIALPURCHASE_P', (469504, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASE_P', (470016, 51)
'get_table_id:bpm.CFDW.DBO.T_CF_CHECKRIGHT_FEETYPE', (470528, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_T_CF_CHECKRIGHT_FEETYPE', (471040, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_APPLICATION', (471552, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_FIN_APPLICATION', (472064, 51)
'get_table_id:bpm.BPMDB.DBO.V_CF_FIN_SHARE_P', (472576, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_CF_FIN_SHARE_P', (473088, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_VQA_SBSCRAPDISPOSE_P_D', (473600, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_VQA_SBSCRAPDISPOSE_P_D', (474112, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SCA_ZEEHODEALERSHOPCHECKANDACCEPT_P_DSTORE', (474624, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPCHECKANDACCEPT_P_DSTORE', (475136, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SCA_ZEEHODEALERSHOPMATERALAPPLY_P_D2', (475648, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPMATERALAPPLY_P_D2', (476160, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZL_QUALITYREPLAYCE', (476672, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_CF_ZL_QUALITYREPLAYCE', (477184, 51)
'get_table_id:bpm.BPMDB.DBO.V_MK_GNKHSP', (477696, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.ODS_V_MK_GNKHSP', (478208, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SSLM_SUPPLIER_CATEGORY', (478720, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_HPFM_EMPLOYEE_USER', (479232, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SPRM_PR_HEADER', (479744, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SPCM_PC_STAGE', (480256, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SSTA_TAX_INVOICE_HEADER', (480768, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SQAM_CLAIM_TYPE', (481280, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_HPFM_TENANT', (481792, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_HPFM_EMPLOYEE', (482304, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SPFM_PARTNER', (482816, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.DIM_SPCM_PC_TYPE', (483328, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SODR_ORDER_TYPE', (483840, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SQAM_CLAIM_FORM_HEADER', (484352, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.DIM_HPFM_UNIT', (484864, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_ACT_RE_PROCDEF', (485376, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_ACT_HI_COMMENT', (485888, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_HPFM_LOV_VALUE', (486400, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.DIM_HPFM_OPERATION_UNIT', (486912, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_ACT_HI_PROCINST', (487424, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SMDM_ITEM_CATEGORY', (487936, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SSTA_SETTLE', (488448, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SPRM_PR_LINE', (488960, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.DIM_SMDM_COST_CENTER', (489472, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SPCM_PC_HEADER', (489984, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SPCM_PC_TABLE_EXTEND', (490496, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_HPFM_TAX', (491008, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SSLM_SUPPLIER_SYNC', (491520, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.DIM_SSLM_SUPPLIER_BASIC', (492032, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SSTA_SETTLE_LINE', (492544, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.DIM_IAM_USER', (493056, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_HPFM_COMPANY', (493568, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_ACT_HI_TASKINST', (494080, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SPCM_PC_SUBJECT', (494592, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SSTA_CHARGE_HEADER', (495104, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SQAM_ED_PROBLEM_HEADER', (495616, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SSTA_CHARGE_LINE', (496128, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SSTA_SETTLE_HEADER', (496640, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SODR_PO_HEADER', (497152, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_ACT_EXCEPTION', (497664, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SQAM_CLAIM_FORM_LINE', (498176, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.ODS_SODR_PO_LINE', (498688, 51)
'get_table_id:SRM.SRM.SRM.ACT_EXCEPTION', (499200, 51)
'get_table_id:SRM.SRM.SRM.SODR_PO_LINE', (499712, 51)
'get_table_id:SRM.SRM.SRM.SQAM_ED_PROBLEM_HEADER', (500224, 51)
'get_table_id:SRM.SRM.SRM.SSTA_CHARGE_LINE', (500736, 51)
'get_table_id:SRM.SRM.SRM.ACT_RE_PROCDEF', (501248, 51)
'get_table_id:SRM.SRM.SRM.ACT_HI_TASKINST', (501760, 51)
'get_table_id:SRM.SRM.SRM.SPCM_PC_STAGE', (502272, 51)
'get_table_id:SRM.SRM.SRM.SQAM_CLAIM_FORM_LINE', (502784, 51)
'get_table_id:SRM.SRM.SRM.SMDM_ITEM_CATEGORY', (503296, 51)
'get_table_id:SRM.SRM.SRM.HPFM_TENANT', (503808, 51)
'get_table_id:SRM.SRM.SRM.SODR_PO_HEADER', (504320, 51)
'get_table_id:SRM.SRM.SRM.SSTA_SETTLE_HEADER', (504832, 51)
'get_table_id:SRM.SRM.SRM.HPFM_LOV_VALUE', (505344, 51)
'get_table_id:SRM.SRM.SRM.SSTA_SETTLE', (505856, 51)
'get_table_id:SRM.SRM.SRM.HPFM_OPERATION_UNIT', (506368, 51)
'get_table_id:SRM.SRM.SRM.SMDM_COST_CENTER', (506880, 51)
'get_table_id:SRM.SRM.SRM.ACT_HI_PROCINST', (507392, 51)
'get_table_id:SRM.SRM.SRM.HPFM_TAX', (507904, 51)
'get_table_id:SRM.SRM.SRM.SPCM_PC_HEADER', (508416, 51)
'get_table_id:SRM.SRM.SRM.SSLM_SUPPLIER_CATEGORY', (508928, 51)
'get_table_id:SRM.SRM.SRM.SPFM_PARTNER', (509440, 51)
'get_table_id:SRM.SRM.SRM.SPCM_PC_TYPE', (509952, 51)
'get_table_id:SRM.SRM.SRM.SPCM_PC_SUBJECT', (510464, 51)
'get_table_id:SRM.SRM.SRM.SPCM_PC_TABLE_EXTEND', (510976, 51)
'get_table_id:SRM.SRM.SRM.SSTA_CHARGE_HEADER', (511488, 51)
'get_table_id:SRM.SRM.SRM.SSLM_SUPPLIER_BASIC', (512000, 51)
'get_table_id:SRM.SRM.SRM.HPFM_UNIT', (512512, 51)
'get_table_id:SRM.SRM.SRM.SSLM_SUPPLIER_SYNC', (513024, 51)
'get_table_id:SRM.SRM.SRM.ACT_HI_COMMENT', (513536, 51)
'get_table_id:SRM.SRM.SRM.IAM_USER', (514048, 51)
'get_table_id:SRM.SRM.SRM.SQAM_CLAIM_TYPE', (514560, 51)
'get_table_id:SRM.SRM.SRM.SSTA_SETTLE_LINE', (515072, 51)
'get_table_id:SRM.SRM.SRM.HPFM_EMPLOYEE', (515584, 51)
'get_table_id:SRM.SRM.SRM.HPFM_COMPANY', (516096, 51)
'get_table_id:SRM.SRM.SRM.HPFM_EMPLOYEE_USER', (516608, 51)
'get_table_id:SRM.SRM.SRM.SPRM_PR_LINE', (517120, 51)
'get_table_id:SRM.SRM.SRM.SODR_ORDER_TYPE', (517632, 51)
'get_table_id:SRM.SRM.SRM.SPRM_PR_HEADER', (518144, 51)
'get_table_id:SRM.SRM.SRM.SSTA_TAX_INVOICE_HEADER', (518656, 51)
'get_table_id:SRM.SRM.SRM.SQAM_CLAIM_FORM_HEADER', (519168, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_PROVINCE', (519680, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_PROVINCE', (520192, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_SALESORDERTYPE', (520704, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_SALESORDERTYPE', (521216, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.V_SUPPLIER_BANK', (521728, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_SUPPLIER_BANK', (522240, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.V_SUPPLIER', (522752, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_SUPPLIER', (523264, 51)
'get_table_id:SAP_DATA.CFD.CFPLM.DIM_VEHICLE_PART_GROUP', (523776, 51)
'get_table_id:bpm.CFDW.DBO.DIM_VEHICLE_PART_GROUP', (524288, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.DIM_PUB_H_CUSTOMER_MAPPING', (524800, 51)
'get_table_id:bpm.CFDW.DBO.DIM_PUB_H_CUSTOMER_MAPPING', (525312, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_TRANSPORTTYPE', (525824, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_TRANSPORTTYPE', (526336, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.DIM_COM_CHINA_AREA_GD', (526848, 51)
'get_table_id:bpm.CFDW.DBO.DIM_COM_CHINA_AREA', (527360, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.DWD_FRM_FICO_YS_DATA_DEPT', (527872, 51)
'get_table_id:bpm.CFDW.DBO.DWD_FRM_FICO_YS_DATA_DEPT', (528384, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_BPM_DEPT_PRIVILEGE_EMP', (528896, 51)
'get_table_id:bpm.CFDW.DBO.DIM_PS_DEPT_PRIVILEGE_EMP', (529408, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_MATERIAL_SOURCE', (529920, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_ORIGINGROUP', (530432, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_ORIGINGROUP', (530944, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DWD_ALL_VEHICLE_BASE_INFO_CFMOTO_R', (531456, 51)
'get_table_id:SAP_DATA.CFD.CFHR.DIM_PS_EMP', (531968, 51)
'get_table_id:bpm.CFDW.DBO.DIM_PS_EMP', (532480, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_PRODUCTGROUP', (532992, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_PRODUCTGROUP', (533504, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_CUSTOMER_BASE', (534016, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_MRPCONTROL', (534528, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_MRPCONTROL', (535040, 51)
'get_table_id:SAP_DATA.CFD.CFSAP800.DIM_TRANSPORTTYPE', (535552, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.V_CF_BPM_RD_PROJECT_BUDGET_SUM', (536064, 51)
'get_table_id:bpm.CFDW.DBO.BPM_RD_PROJECT_BUDGET_SUM', (536576, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_CURRENCY', (537088, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_MAN_CURRENCY', (537600, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_SALESDEPT', (538112, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_SALESDEPT', (538624, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_SAP_MATERIAL_CHECK', (539136, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_MATERIAL_CHECK', (539648, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.DWD_FRM_FICO_YS_DATA_TZ', (540160, 51)
'get_table_id:bpm.CFDW.DBO.DWD_FRM_FICO_YS_DATA_TZ', (540672, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_BPM_NEAREXPI_PURPRICE_WARN', (541184, 51)
'get_table_id:bpm.CFDW.DBO.DIM_FR_M_NEAREXPI_PURPRICE_WARN', (541696, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_STOCKPLACE', (542208, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_STOCKPLACE', (542720, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_BPM_LEAVED_EMP_LOAN_SUM', (543232, 51)
'get_table_id:bpm.CFDW.DBO.DWD_SAP_D_LEAVED_EMP_LOAN', (543744, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DWD_ALL_VEHICLE_BASE_INFO_R', (544256, 51)
'get_table_id:bpm.CFDW.DBO.DWD_ALL_VEHICLE_BASE_INFO', (544768, 51)
'get_table_id:bpm.CFDW.DBO.CF_CW_BGINVOICE', (545280, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_BPM_UNSALE_VEHICLE_FILL', (545792, 51)
'get_table_id:bpm.CFDW.DBO.DWD_FR_UNSALE_VEHICLE_FILL', (546304, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_BPM_LAST_PURCON_PRICE', (546816, 51)
'get_table_id:bpm.CFDW.DBO.DWD_PURCH_LASTPURCONPRICE_CF', (547328, 51)
'get_table_id:SAP_DATA.CFD.CFDMS.ODS_V_KDVEHICLEINFO', (547840, 51)
'get_table_id:bpm.CFDW.DBO.DWD_DMS_KDVEHICLEINFO', (548352, 51)
'get_table_id:SAP_DATA.CFD.CFFR.FRM_FICO_YS_DIM_KM', (548864, 51)
'get_table_id:bpm.CFDW.DBO.DIM_FRM_FICO_YS_DIM_KM', (549376, 51)
'get_table_id:SAP_DATA.CFD.CFHR.DIM_PS_DEPT_ALL', (549888, 51)
'get_table_id:bpm.CFDW.DBO.DIM_PS_DEPT', (550400, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_MATERIAL_FACTORY', (550912, 51)
'get_table_id:SAP_DATA.CFD.CFCOM.DIM_FT_CARTYPE_PACK', (551424, 51)
'get_table_id:bpm.CFDW.DBO.DIM_FT_CARTYPE_PACK', (551936, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_STOCKTYPE', (552448, 51)
'get_table_id:bpm.CFDW.DBO.ODS_SAP_D_STOCKTYPE', (552960, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_FACTORY', (553472, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_MAN_FACTORY', (553984, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_MONTHCURRENCY', (554496, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_MONTHCURRENCY', (555008, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.DWD_CF_SAP_D_DZ_AGE', (555520, 51)
'get_table_id:bpm.CFDW.DBO.DWD_CF_SAP_D_DZ_AGE', (556032, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_SAP_MATERIAL_ASSESS', (556544, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_MATERIAL_ASSESS', (557056, 51)
'get_table_id:SAP_DATA.CFD.CFHR.DIM_DEPTWITHCOSTCENTERINFO', (557568, 51)
'get_table_id:bpm.CFDW.DBO.DIM_DEPTWITHCOSTCENTERINFO', (558080, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_MATERIALTYPE', (558592, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_MATERIALTYPE', (559104, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_BPM_SAP_BGINVOICE', (559616, 51)
'get_table_id:SAP_DATA.CFD.CFSAPBC.DWD_PP_VEHICLE_CERTIFICATION', (560128, 51)
'get_table_id:bpm.CFDW.DBO.DIM_PUB_D_VEHICLECERTIFICATION', (560640, 51)
'get_table_id:bpm.CFDW.DBO.DWD_PURCH_H_LASTPURPRICE', (561152, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_SALESDEPTANDGROUP', (561664, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_SALESDEPTANDGROUP', (562176, 51)
'get_table_id:SAP_DATA.CFD.CFHR.DIM_PS_DEPT_RELA', (562688, 51)
'get_table_id:bpm.CFDW.DBO.DIM_PS_DEPT_RELA', (563200, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_BPM_SAP_SYSTEM_ACCOUNT', (563712, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_SYSTEM_ACCOUNT', (564224, 51)
'get_table_id:SAP_DATA.CFD.CFFR.FRM_FICO_YS_DIM_CBDEPT', (564736, 51)
'get_table_id:bpm.CFDW.DBO.DIM_FRM_FICO_YS_DIM_CBDEPT', (565248, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_SAP_MATERIAL_FACTORY', (565760, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_RECEIVEANDPAYTERM', (566272, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_RECEIVEANDPAYTERM', (566784, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_STOCKNUMBER', (567296, 51)
'get_table_id:bpm.CFDW.DBO.ODS_SAP_D_STOCKNUMBER', (567808, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_BPM_SAP_NOCHECK_MATE_BATCH', (568320, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_NOCHECK_MATE_BATCH', (568832, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_PROFITCENTER', (569344, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_PROFITCENTER', (569856, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.DIM_SRM_SUPPLIER_PURCLASS', (570368, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SRM_SUPPLIER_PURCLASS', (570880, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DWD_ALL_VEHICLE_BASE_INFO', (571392, 51)
'get_table_id:bpm.CFDW.DBO.DWD_PURCH_D_LASTPURPRICE_ALL', (571904, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_MATERIAL', (572416, 51)
'get_table_id:SAP_DATA.CFD.CFCOM.ODS_EAM_ASSET_LOCATION', (572928, 51)
'get_table_id:bpm.CFDW.DBO.DIM_EAM_ASSET_LOCATION', (573440, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_CUSTOMER_SALES', (573952, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_ASSESSMENT', (574464, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_ASSESSMENT', (574976, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_BPM_VEHICLE_CONFIG_INFO', (575488, 51)
'get_table_id:bpm.CFDW.DBO.DIM_VEHICLE_CONFIG_INFO', (576000, 51)
'get_table_id:bpm.CFDW.DBO.MATERIAL_PURCHASE_INFO', (576512, 51)
'get_table_id:SAP_DATA.CFD.CFPLM.DIM_PLM_MATERIAL', (577024, 51)
'get_table_id:bpm.CFDW.DBO.DIM_PLM_D_MATERIAL', (577536, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_CUSTOMER', (578048, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.TMP_MAX_STEPID', (578560, 51)
'get_table_id:bpm.CFDW.DBO.TMP_MAX_STEPID', (579072, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.DWD_VEHICLECERTIFICATION', (579584, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_COUNTRY', (580096, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_COUNTRY', (580608, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_BPM_SAP_NO_PAYMENT', (581120, 51)
'get_table_id:bpm.CFMOTO.DBO.T_SAP_PAYMENT', (581632, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_PROCESSSECTION', (582144, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_PROCESSSECTION', (582656, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_PURCHASETYPE', (583168, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_FIX_PURCHASETYPE', (583680, 51)
'get_table_id:bpm.CFDW.DBO.DWD_PURCH_H_PRICE_DTL', (584192, 51)
'get_table_id:SAP_DATA.CFD.CFDMS.DIM_DMS_VEHICLE', (584704, 51)
'get_table_id:bpm.CFDW.DBO.DIM_DMS_VEHICLE', (585216, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_PURCHASEGROUP', (585728, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_PURCHASEGROUP', (586240, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_TAXTYPE', (586752, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_TAXTYPE', (587264, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_SALESGROUP', (587776, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_SALESGROUP', (588288, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DWD_SAP_VEHICLE_BASE_INFO', (588800, 51)
'get_table_id:bpm.CFDW.DBO.DWD_COM_MIN_VEHICLEBASEINFO', (589312, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_BANKACCOUNT', (589824, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_BANKACCOUNT', (590336, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_SALESORG', (590848, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_MAN_SALESORG', (591360, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_SAP_CARTYPE_TJ', (591872, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_CARTYPE_TJ', (592384, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_BPM_PS_PERF_EMPL_LEVEL', (592896, 51)
'get_table_id:bpm.CFDW.DBO.DIM_PS_D_PERF_EMPL_LEVEL', (593408, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_UNIT', (593920, 51)
'get_table_id:bpm.CFDW.DBO.CF_SAP_MDM_MATERIALUNIT', (594432, 51)
'get_table_id:bpm.CFMOTO.DBO.CF_MK_SAP_COSTCENTER', (594944, 51)
'get_table_id:bpm.CFDW.DBO.ODS_SAP_MIN_V_INNERORDER', (595456, 51)
'get_table_id:SAP_DATA.CFD.CFAPP.ODS_V_TWOTBOX_INFO', (595968, 51)
'get_table_id:bpm.CFDW.DBO.ODS_APP_D_TWOTBOX_INFO', (596480, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DWD_SAP_INVENTORY', (596992, 51)
'get_table_id:bpm.CFDW.DBO.T_SAP_INVENTORY', (597504, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_SAP_EXCHANGE_RATE', (598016, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_EXCHANGE_RATE', (598528, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.DIM_PUB_CARTYPEINFO', (599040, 51)
'get_table_id:bpm.CFDW.DBO.DIM_PUB_H_CARTYPEINFO', (599552, 51)
'get_table_id:bpm.CFDW.DBO.CF_SAP_PURCHASE_CATEGORY', (600064, 51)
'get_table_id:bpm.CFDW.DBO.DWD_SALES_H_SALESPRICE_CUST', (600576, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.DIM_EAM_ASSET', (601088, 51)
'get_table_id:bpm.CFDW.DBO.DIM_EAM_ASSET', (601600, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_COMPANY', (602112, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_MAN_COMPANY', (602624, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.DWD_FRM_FICO_YS_DATA_XM', (603136, 51)
'get_table_id:bpm.CFDW.DBO.DWD_FRM_FICO_YS_DATA_XM', (603648, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.DIM_VEHICLE_MODEL_SITE_RELATION', (604160, 51)
'get_table_id:bpm.CFDW.DBO.DIM_VEHICLE_MODEL_SITE_RELATION', (604672, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_SAP_ASSET_EFF', (605184, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_ASSET_EFF', (605696, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.DIM_EXCHANGE_RATE_BEGIN_MONTH', (606208, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_MONTHCURRENCY_START', (606720, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_UNIT', (607232, 51)
'get_table_id:SAP_DATA.CFD.CFFR.FRM_DIM_INF_OA', (607744, 51)
'get_table_id:bpm.CFDW.DBO.DIM_DATAOA_INFO', (608256, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_ACCOUNT', (608768, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_ACCOUNT', (609280, 51)
'get_table_id:bpm.CFDW.DBO.DWD_SALES_H_SALESPRICE_TYPE', (609792, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_SUPPLIER_PURCHASE', (610304, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_BPM_FR_FREIGHT_SETTLE', (610816, 51)
'get_table_id:bpm.CFDW.DBO.DWD_FR_FREIGHT_SETTLE', (611328, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_CUSTOMER_ADDRESS', (611840, 51)
'get_table_id:SAP_DATA.CFD.CFFR.DWD_FINE_REPORT_DTL', (612352, 51)
'get_table_id:bpm.CFDW.DBO.DWD_FINE_REPORT_DTL', (612864, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.V_CF_DMS_MATERIAL_NOPRICE_2BPM', (613376, 51)
'get_table_id:bpm.CFDW.DBO.DMS_MATERIAL_NOPRICE_2BPM', (613888, 51)
'get_table_id:SAP_DATA.CFD.CFFR.DWD_FRM_INF_JSC', (614400, 51)
'get_table_id:bpm.CFDW.DBO.DWD_FRM_INF_JSC', (614912, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_MATERIALGROUP', (615424, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_MATERIALGROUP', (615936, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_PURCHASEORG', (616448, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_MAN_PURCHASEORG', (616960, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_COSTCENTER', (617472, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_D_COSTCENTER', (617984, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_VEHICLEINFO', (618496, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_OTA_CARTYPE_PART_INFO', (619008, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_DMS_SJBG', (619520, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SRM_SUPP_PURCPRICE_MATCH_DTL', (620032, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_APP_TBOX_CARTYPE_REL', (620544, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DWD_GN_OUT_DETAIL', (621056, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_TRANSPORTTYPE', (621568, 51)
'get_table_id:SAP_DATA.CFD.CFAPP.ODS_V_EXTERNAL_FINANCE_INSURANCE', (622080, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_APP_REPAIR_ORDER_WORKHOURS', (622592, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.DIM_DEALER_STORE_CONTACT', (623104, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_SALESORDERTYPE', (623616, 51)
'get_table_id:SAP_DATA.CFD.CFAPP.ODS_V_EXTERNAL_FINANCE_REVIEW', (624128, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_COUNTRY', (624640, 51)
'get_table_id:SAP_DATA.CFD.CFFR.FR_FTT_OFFLINE_QUALIFY_RATE', (625152, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SRM_PROJECT_MEMBER_LIST', (625664, 51)
'get_table_id:SAP_DATA.CFD.CFDMS.ODS_FRM_DMS_LS_V', (626176, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_API_ENGINE_YEAR', (626688, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_SALESGROUP', (627200, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SRM_PROJECT_SPECIFICPARTS_LIST', (627712, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.ADS_APP_MIN_BOOKINGORDER', (628224, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_OTA_VEHICLE_PART_INFO', (628736, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_SALESORG', (629248, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_ZDY_DMS_VEHICLE_BASE', (629760, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.DWD_DMS_VEHICLE_SALES_DETAIL', (630272, 51)
'get_table_id:SAP_DATA.CFD.CFHR.DIM_YTHR_DEPT', (630784, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_VEHICLEINFO_OTA', (631296, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SRM_VQA_DESIGNCHANGEDEVELOPMENT', (631808, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_SAP_MATERIAL_REPLACE', (632320, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_MDM_DEALER_STORE_TRY', (632832, 51)
'get_table_id:SAP_DATA.CFD.CFEV.ODS_FDDEF_APP', (633344, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_APP_REPAIR_ORDER_PARTS', (633856, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_APP_VEHICLE_SPECIFICATION', (634368, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_SALESPRICETYPE', (634880, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SRM_COSTSHARE_LIST', (635392, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_OTA_VEHICLE_BASE_INFO', (635904, 51)
'get_table_id:SAP_DATA.CFD.CFBPM.DWD_SALE_DOMESTICCUSTOMERREFUNDORDER_CZ', (636416, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_APP_TBOX_CARTYPE_REL2', (636928, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_POSITION', (637440, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_PRODUCTGROUP', (637952, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_MDM_DEALER_STORE', (638464, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_MATERIAL_VC', (638976, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_YB_CUSTOMER', (639488, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_ORDERREASON', (640000, 51)
'get_table_id:SAP_DATA.CFD.CFSAP.DIM_SIZE', (640512, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_SALESCHANNEL', (641024, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_ZEEHO_SPECIAL_OUT', (641536, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_ACCOUNTTYPE', (642048, 51)
'get_table_id:SAP_DATA.CFD.CFAPP.ODS_V_EXTERNAL_FINANCE_LOAN', (642560, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SRM_COSTSHARE_LIST', (643072, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_MATERIAL_FACTORY', (643584, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_DMS_FERTH_VSBED', (644096, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_MDM_DEALER_STORE_CONTACT', (644608, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_SIZE', (645120, 51)
'get_table_id:SAP_DATA.CFD.CFHR.DIM_YTHR_EMP', (645632, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_HR_EMP', (646144, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.ODS_SAP_MIN_V_INNERORDER', (646656, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_PURCHASEGROUP', (647168, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_MATERIAL_EX', (647680, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_APP_TBOX_CARTYPE_REL_20240314', (648192, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_MDM_VEHICLE_SALESINFO', (648704, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_MDM_VEHICLE_SALESINFO', (649216, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DIM_SAP_H_CUSTOMER_PARTNER', (649728, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.ADS_APP_D_CARTYPE', (650240, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.ADS_APP_D_CARTYPE', (650752, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_DMS_SJBG', (651264, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DIM_SAP_D_COSTCENTER', (651776, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_OTA_MMI_REPLACE_RECORD', (652288, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_OTA_MMI_REPLACE_RECORD', (652800, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_OTA_VERSION_UPGRADE_CONFIRMATION', (653312, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_OTA_VERSION_UPGRADE_CONFIRMATION', (653824, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_TWO_FTT_OFFLINE_QUALIFY_RATE', (654336, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_APP_VEHICLE_SALES_DETAIL', (654848, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_TRANSPORTTYPE', (655360, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_OTA_VEHICLE_BASE_INFO', (655872, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DIM_SAP_H_CUSTOMER_RATIO', (656384, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_MDM_DEALER_MATERIAL_R', (656896, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_MDM_DEALER_MATERIAL', (657408, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_ODS_V_EXTERNAL_FINANCE_LOAN', (657920, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_SALESGROUP', (658432, 51)
'get_table_id:SAP_DATA.CFD.CFSRM.SRM_MX_VQA_WASTEQUANTITY', (658944, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.SRM_MX_VQA_WASTEQUANTITY', (659456, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_COUNTRY', (659968, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_SALESPRICE_COMPANY', (660480, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_OTA_VEHICLE_PART_INFO', (660992, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_SALESORG', (661504, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_MDM_ZMDM_CJX', (662016, 51)
'get_table_id:SAP_DATA.CFD.CFDMS.DWD_CUSTOMER_ORDER_STATUS', (662528, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_DMS_CUSTOMER_ORDER_STATUS', (663040, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_APP_VEHICLE_SPECIFICATION', (663552, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_VEHICLEINFO', (664064, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DIM_SAP_H_CUSTOMER_STORE', (664576, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.ADS_APP_MIN_OUTSTOCK', (665088, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.ADS_APP_MIN_OUTSTOCK', (665600, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_ODS_V_EXTERNAL_FINANCE_INSURANCE', (666112, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_DMS_GWKHTK', (666624, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.ADS_APP_D_TBOXCHANGELIST', (667136, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.ADS_APP_D_TBOXCHANGELIST', (667648, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_APP_REPAIR_ORDER_WORKHOURS', (668160, 51)
'get_table_id:SAP_DATA.CFD.CFHR.DIM_PS_DEPT', (668672, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_HR_DEPT', (669184, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_APP_VEHICLE_SPECIFICATION_FILL', (669696, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_APP_CP_CARTYPE_REL', (670208, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_APP_CP_CARTYPE_REL', (670720, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.ADS_APP_MIN_BOOKINGORDER', (671232, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_BANKACCOUNT', (671744, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_OTA_CARTYPE_PART_INFO', (672256, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SRM_PROJECT_MEMBER_LIST', (672768, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_APP_TBOX_CARTYPE_REL', (673280, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DIM_SAP_MATERIAL_REPLACE', (673792, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DIM_SAP_H_CUSTOMER', (674304, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_ORDERREASON', (674816, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DIM_SAP_H_MATERIAL', (675328, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SRM_SUPP_PURCPRICE_MATCH_DTL', (675840, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DWD_SALES_H_SALESPRICE_CUST_VC', (676352, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.TEST_ZJF', (676864, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_ODS_V_EXTERNAL_FINANCE_REVIEW', (677376, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SRM_PROJECT_SPECIFICPARTS_LIST', (677888, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_SAP_SALESDEPT', (678400, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_SALESDEPT', (678912, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_POSITION', (679424, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_SALESCHANNEL', (679936, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_DMS_GNKHSP', (680448, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_SALESORDERTYPE', (680960, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DWD_SALES_H_SALESPRICE_TYPE_VC', (681472, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DIM_SAP_H_CUSTOMER_SALES', (681984, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_ODS_GN_OUT_DETAIL', (682496, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_PRODUCTGROUP', (683008, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_YB_CUSTOMER', (683520, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_CUSTOMER_BASE', (684032, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_ODS_FRM_DMS_LS_V', (684544, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DIM_COM_CHINA_AREA_GD', (685056, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_MDM_DEALER_STORE', (685568, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_DMS_XSDDPS', (686080, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_APP_EV_ODS_FDDEF_APP', (686592, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_SALESPRICETYPE', (687104, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SRM_VQA_DESIGNCHANGEDEVELOPMENT', (687616, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_FOREIGNPART_OUT_SUM', (688128, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_DMS_SBJY', (688640, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_BPM_SALE_DOMESTICCUSTOMERREFUNDORDER_CZ', (689152, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_ZDY_DMS_VEHICLE_BASE', (689664, 51)
'get_table_id:SAP_DATA.CFD.CFUIP.ODS_ISOC_ETL_LOG', (690176, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.ISOC_ETL_LOG', (690688, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DIM_SAP_H_SUPPLIER', (691200, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_DMS_GNKHFKSQ', (691712, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_DMS_GWKHSP', (692224, 51)
'get_table_id:SAP_DATA.CFD.CFDATA.CF_APP_REPAIR_ORDER', (692736, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_APP_REPAIR_ORDER', (693248, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DWD_SALES_H_SALESPRICE_CUST', (693760, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_ACCOUNTTYPE', (694272, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_MATERIAL_VC', (694784, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DIM_SAP_H_CUSTOMER_ADDRESS', (695296, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_APP_TBOX_CARTYPE_REL2', (695808, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_APP_REPAIR_ORDER_PARTS', (696320, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_MDM_DEALER_STORE_TRY', (696832, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_UNIT', (697344, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.DWD_SALES_H_SALESPRICE_TYPE', (697856, 51)
'get_table_id:data-center.CFD.CFSRM.DIM_SPCM_PC_TYPE', (698368, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_CP_CARTYPE_REL', (698880, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_CF_ENGINE_MACHINE', (699392, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_CF_PORT_DELIVERY_RECORD', (699904, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_CF_ASSEMBLY_PLAN', (700416, 51)
'get_table_id:data-center.CFD.CFSAPBC.TMP_V_CF_BARCODE_BIND_DELETE', (700928, 51)
'get_table_id:data-center.CFD.CFSAPBC.TMP_CF_BARCODE_INVENTORY_DELETE', (701440, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_SYS_USER', (701952, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_CF_BARCODE_INVENTORY', (702464, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_V_CF_BARCODE_BIND', (702976, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_CF_SALE_INVOICE_RECORD', (703488, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_CF_BARCODE_MATERIALS', (704000, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_CF_MACHINING_ALUMINUM', (704512, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_V_TCER_COQ_INLAND_TWO_PRINT_UPLOAD_RECORD', (705024, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_TBOX_CARTYPE_REL', (705536, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_CF_REPORT_WORK_RECORD', (706048, 51)
'get_table_id:data-center.CFD.CFSAPBC.DIM_CF_KTM_RECEIVING_ORDER', (706560, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_TCER_COQ_INLAND_FOUR_PRINT_UPLOAD_RECORD', (707072, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_CF_KTM_RECEIVING_ORDER', (707584, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_CF_BARCODE_KEY_PART', (708096, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_CF_LOAD_PACKING', (708608, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_CF_SALE_INVOICE_HEADER', (709120, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_TCER_COQ_INLAND_TWO_PRINT_UPLOAD_RECORD', (709632, 51)
'get_table_id:data-center.CFD.CFSAPBC.ODS_TBOX_CARTYPE_REL_TEST', (710144, 51)
'get_table_id:data-center.CFD.CFCOM.ODS_QIS_V_CARTYPE', (710656, 51)
'get_table_id:data-center.CFD.CFCOM.ODS_QIS_FTT_CJH_CARTYPE', (711168, 51)
'get_table_id:data-center.CFD.CFCOM.ODS_QIS_MATERIAL_INFORMATION', (711680, 51)
'get_table_id:data-center.CFD.CFCOM.ODS_QIS_VEHICLE_PROPERTIES', (712192, 51)
'get_table_id:data-center.CFD.CFCOM.ODS_QIS_FAULT_INFORMATION', (712704, 51)
'get_table_id:data-center.CFD.CFCOM.ODS_QIS_FTT_GATHER', (713216, 51)
'get_table_id:data-center.CFD.CFCOM.ODS_QIS_CUSTOMER_COMPLAINTS', (713728, 51)
'get_table_id:data-center.CFD.CFCOM.ODS_QIS_ZJY_INFORMATION', (714240, 51)
'get_table_id:data-center.CFD.CFCOM.ODS_QIS_FTT_CX', (714752, 51)
'get_table_id:data-center.CFD.CFCOM.ODS_QIS_PRODUCT_EVALUATION_PROBLEM', (715264, 51)
'get_table_id:data-center.CFD.CFCOM.ODS_QIS_FTT_GX', (715776, 51)
'get_table_id:data-center.CFD.CFCOM.ODS_QIS_ZYRY_INFORMATION', (716288, 51)
'get_table_id:data-center.CFD.CFTEST.ODS_QIS_FAULT_INFORMATION', (716800, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_HPFM_EMPLOYEE_USER', (717312, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_HPFM_TAX', (717824, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_HPFM_TENANT', (718336, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SSTA_SETTLE_HEADER', (718848, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SSTA_TAX_INVOICE_HEADER', (719360, 51)
'get_table_id:data-center.CFD.CFSRM.DIM_HPFM_OPERATION_UNIT', (719872, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SPCM_PC_SUBJECT', (720384, 51)
'get_table_id:data-center.CFD.CFSRM.DIM_IAM_USER', (720896, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_HPFM_EMPLOYEE', (721408, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_HPFM_LOV_VALUE', (721920, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SPCM_PC_STAGE', (722432, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SSTA_SETTLE_LINE', (722944, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SSTA_CHARGE_HEADER', (723456, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SSTA_SETTLE', (723968, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SODR_ORDER_TYPE', (724480, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SPCM_PC_TABLE_EXTEND', (724992, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_HPFM_COMPANY', (725504, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_ACT_HI_COMMENT', (726016, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SQAM_CLAIM_FORM_LINE', (726528, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SODR_PO_LINE', (727040, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_ACT_HI_TASKINST', (727552, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SQAM_ED_PROBLEM_HEADER', (728064, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SSLM_SUPPLIER_CATEGORY', (728576, 51)
'get_table_id:data-center.CFD.CFSRM.DIM_HPFM_UNIT', (729088, 51)
'get_table_id:data-center.CFD.CFSRM.DIM_SSLM_SUPPLIER_BASIC', (729600, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SPRM_PR_LINE', (730112, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SPRM_PR_HEADER', (730624, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SSTA_CHARGE_LINE', (731136, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SODR_PO_HEADER', (731648, 51)
'get_table_id:data-center.CFD.CFSRM.DIM_SMDM_COST_CENTER', (732160, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SPFM_PARTNER', (732672, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SPCM_PC_HEADER', (733184, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SSLM_SUPPLIER_SYNC', (733696, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_ACT_HI_PROCINST', (734208, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SQAM_CLAIM_TYPE', (734720, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_ACT_EXCEPTION', (735232, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_ACT_RE_PROCDEF', (735744, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SMDM_ITEM_CATEGORY', (736256, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SQAM_CLAIM_FORM_HEADER', (736768, 51)
'get_table_id:bpm.CFDW.DBO.SRM_IAM_USER', (737280, 51)
'get_table_id:bpm.CFDW.DBO.SPRM_PR_HEADER', (737792, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_ZMMT_BC_18', (738304, 51)
'get_table_id:data-center.CFD.CFFR.FRM_DATEYM', (738816, 51)
'get_table_id:data-center.CFD.CFFR.FRM_DIM_DATEYMD', (739328, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_ZMMT_BC_19', (739840, 51)
'get_table_id:data-center.CFD.CFDATA.DWD_VEHICLECERTIFICATION', (740352, 51)
'get_table_id:data-center.CFD.CFFR.V_FR_PP_DEPT_REACTION', (740864, 51)
'get_table_id:data-center.CFD.CFSAPBC.DWD_PP_VEHICLE_CERTIFICATION', (741376, 51)
'get_table_id:data-center.CFD.CFFR.FR_PP_ORDER_TYPE', (741888, 51)
'get_table_id:data-center.CFD.CFHR.DIM_DEPTWITHCOSTCENTERINFO', (742400, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI_HRBM_CBZX', (742912, 51)
'get_table_id:data-center.CFD.CFDATA.CF_BPM_UNSALE_VEHICLE_FILL', (743424, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_SAP_CARTYPE_TJ', (743936, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_PURCHASEGROUP', (744448, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_SALESDEPT', (744960, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTCGPL', (745472, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_PROFITCENTER', (745984, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_PROCESSSECTION', (746496, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_MATERIAL_SOURCE', (747008, 51)
'get_table_id:data-center.CFD.CFHR.DIM_PS_DEPT_ALL', (747520, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_BANKACCOUNT', (748032, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_PURCHASEORG', (748544, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_UNIT', (749056, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_SALESDEPTANDGROUP', (749568, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_TRANSPORTTYPE', (750080, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_SAP_MATERIAL_CHECK', (750592, 51)
'get_table_id:data-center.CFD.CFDATA.CF_MK_SAP_COSTCENTER', (751104, 51)
'get_table_id:data-center.CFD.CFDATA.DIM_PUB_CARTYPEINFO', (751616, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_PROVINCE', (752128, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_SALESORDERTYPE', (752640, 51)
'get_table_id:data-center.CFD.CFFR.FRM_FICO_YS_DIM_CBDEPT', (753152, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_MRPCONTROL', (753664, 51)
'get_table_id:data-center.CFD.CFSAP.DWD_ALL_VEHICLE_BASE_INFO_CFMOTO_R', (754176, 51)
'get_table_id:data-center.CFD.CFDATA.DIM_EXCHANGE_RATE_BEGIN_MONTH', (754688, 51)
'get_table_id:data-center.CFD.CFFR.DWD_FRM_INF_JSC', (755200, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_CUSTOMER_BASE', (755712, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_TAXTYPE', (756224, 51)
'get_table_id:data-center.CFD.CFSAP.DWD_ALL_VEHICLE_BASE_INFO', (756736, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_COMPANY', (757248, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_RECEIVEANDPAYTERM', (757760, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_SUPPLIER_PURCHASE', (758272, 51)
'get_table_id:data-center.CFD.CFDATA.CF_BPM_SAP_NOCHECK_MATE_BATCH', (758784, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_ASSESSMENT', (759296, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_MONTHCURRENCY', (759808, 51)
'get_table_id:data-center.CFD.CFBPM.TMP_MAX_STEPID', (760320, 51)
'get_table_id:data-center.CFD.CFSAP.DWD_MATERIAL_PURCHASE_INFO_D', (760832, 51)
'get_table_id:data-center.CFD.CFBPM.DWD_CF_SAP_D_DZ_AGE', (761344, 51)
'get_table_id:data-center.CFD.CFDATA.CF_BPM_SAP_BGINVOICE', (761856, 51)
'get_table_id:data-center.CFD.CFDATA.CF_BPM_LAST_PURCON_PRICE', (762368, 51)
'get_table_id:data-center.CFD.CFDATA.V_CF_DMS_MATERIAL_NOPRICE_2BPM', (762880, 51)
'get_table_id:data-center.CFD.CFCOM.DIM_FT_CARTYPE_PACK', (763392, 51)
'get_table_id:data-center.CFD.CFFR.FRM_DIM_INF_OA', (763904, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_BPMINSTPROCSTEPS', (764416, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_CURRENCY', (764928, 51)
'get_table_id:data-center.CFD.CFBPM.DWD_FRM_FICO_YS_DATA_XM', (765440, 51)
'get_table_id:data-center.CFD.CFAPP.ODS_V_TWOTBOX_INFO', (765952, 51)
'get_table_id:data-center.CFD.CFDATA.CF_BPM_FR_FREIGHT_SETTLE', (766464, 51)
'get_table_id:data-center.CFD.CFDATA.CF_BPM_VEHICLE_CONFIG_INFO', (766976, 51)
'get_table_id:data-center.CFD.CFHR.DIM_PS_EMP', (767488, 51)
'get_table_id:data-center.CFD.CFDATA.V_CF_BPM_RD_PROJECT_BUDGET_SUM', (768000, 51)
'get_table_id:data-center.CFD.CFDATA.CF_BPM_SAP_NO_PAYMENT', (768512, 51)
'get_table_id:data-center.CFD.CFDATA.CF_BPM_LEAVED_EMP_LOAN_SUM', (769024, 51)
'get_table_id:data-center.CFD.CFFR.FRM_FICO_YS_DIM_KM', (769536, 51)
'get_table_id:data-center.CFD.CFPLM.DIM_VEHICLE_PART_GROUP', (770048, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_SAP_MATERIAL_ASSESS', (770560, 51)
'get_table_id:data-center.CFD.CFDATA.CF_BPM_PS_PERF_EMPL_LEVEL', (771072, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_MATERIALTYPE', (771584, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_MATERIALGROUP', (772096, 51)
'get_table_id:data-center.CFD.CFSAP.DWD_SAP_INVENTORY', (772608, 51)
'get_table_id:data-center.CFD.CFSAP.V_SUPPLIER', (773120, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_LASTPURCHASEPRICE', (773632, 51)
'get_table_id:data-center.CFD.CFBPM.DWD_FRM_FICO_YS_DATA_DEPT', (774144, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_PURCHASEPRICE_DTL', (774656, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_COSTCENTER', (775168, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_SAP_EXCHANGE_RATE', (775680, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_SALESORG', (776192, 51)
'get_table_id:data-center.CFD.CFSAP.V_SUPPLIER_BANK', (776704, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_SAP_MATERIAL_FACTORY', (777216, 51)
'get_table_id:data-center.CFD.CFDATA.DIM_PUB_H_CUSTOMER_MAPPING', (777728, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_PRODUCTGROUP', (778240, 51)
'get_table_id:data-center.CFD.CFSAP.DWD_SAP_VEHICLE_BASE_INFO', (778752, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_SALESPRICE_TYPE', (779264, 51)
'get_table_id:data-center.CFD.CFDATA.DIM_COM_CHINA_AREA_GD', (779776, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_CW_BGINVOICE', (780288, 51)
'get_table_id:data-center.CFD.CFDATA.CF_BPM_DEPT_PRIVILEGE_EMP', (780800, 51)
'get_table_id:data-center.CFD.CFSAP.DWD_ALL_VEHICLE_BASE_INFO_R', (781312, 51)
'get_table_id:data-center.CFD.CFSRM.DIM_SRM_SUPPLIER_PURCLASS', (781824, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_V_KDVEHICLEINFO', (782336, 51)
'get_table_id:data-center.CFD.CFDATA.DIM_VEHICLE_MODEL_SITE_RELATION', (782848, 51)
'get_table_id:data-center.CFD.CFHR.DIM_PS_DEPT_RELA', (783360, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_CUSTOMER', (783872, 51)
'get_table_id:data-center.CFD.CFSAP800.DIM_TRANSPORTTYPE', (784384, 51)
'get_table_id:data-center.CFD.CFDATA.CF_BPM_NEAREXPI_PURPRICE_WARN', (784896, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_CUSTOMER_ADDRESS', (785408, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_SAP_ASSET_EFF', (785920, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_PURCHASETYPE', (786432, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_ACCOUNT', (786944, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_ORIGINGROUP', (787456, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_LAST_PURC_PRICE_ALL', (787968, 51)
'get_table_id:data-center.CFD.CFDATA.CF_BPM_SAP_SYSTEM_ACCOUNT', (788480, 51)
'get_table_id:data-center.CFD.CFFR.DWD_FINE_REPORT_DTL', (788992, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_MATERIAL_FACTORY', (789504, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_STOCKPLACE', (790016, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_SALESGROUP', (790528, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_COUNTRY', (791040, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_STOCKNUMBER', (791552, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_STOCKTYPE', (792064, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_FACTORY', (792576, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_CUSTOMER_SALES', (793088, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_MATERIAL', (793600, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_INNERORDER', (794112, 51)
'get_table_id:data-center.CFD.CFBPM.DWD_FRM_FICO_YS_DATA_TZ', (794624, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_PURCHASEPRICE', (795136, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_SALESPRICE_CUST', (795648, 51)
'get_table_id:data-center.CFD.CFDMS.DIM_DMS_VEHICLE', (796160, 51)
'get_table_id:data-center.CFD.CFCOM.ODS_EAM_ASSET_LOCATION', (796672, 51)
'get_table_id:data-center.CFD.CFDATA.DIM_EAM_ASSET', (797184, 51)
'get_table_id:data-center.CFD.CFPLM.DIM_PLM_MATERIAL', (797696, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_DMS_GWKHTK', (798208, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_SALESPRICE_COMPANY', (798720, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_VEHICLEINFO_OTA', (799232, 51)
'get_table_id:data-center.CFD.CFDATA.CF_APP_REPAIR_ORDER_WORKHOURS', (799744, 51)
'get_table_id:data-center.CFD.CFDATA.CF_APP_VEHICLE_SPECIFICATION', (800256, 51)
'get_table_id:data-center.CFD.CFDATA.ADS_APP_MIN_OUTSTOCK', (800768, 51)
'get_table_id:data-center.CFD.CFDATA.CF_APP_CP_CARTYPE_REL', (801280, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_CUSTOMER_STORE', (801792, 51)
'get_table_id:data-center.CFD.CFDATA.CF_ZEEHO_SPECIAL_OUT', (802304, 51)
'get_table_id:data-center.CFD.CFAPP.ODS_V_EXTERNAL_FINANCE_INSURANCE', (802816, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SRM_PROJECT_MEMBER_LIST', (803328, 51)
'get_table_id:data-center.CFD.CFDATA.CF_APP_TBOX_CARTYPE_REL', (803840, 51)
'get_table_id:data-center.CFD.CFDATA.ADS_APP_D_TBOXCHANGELIST', (804352, 51)
'get_table_id:data-center.CFD.CFEV.ODS_FDDEF_APP', (804864, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_COUNTRY', (805376, 51)
'get_table_id:data-center.CFD.CFDATA.CF_APP_TBOX_CARTYPE_REL2', (805888, 51)
'get_table_id:data-center.CFD.CFDATA.CF_APP_VEHICLE_SPECIFICATION_FILL', (806400, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_MATERIAL_VC', (806912, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_POSITION', (807424, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_FRM_DMS_LS_V', (807936, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_DMS_GNKHFKSQ', (808448, 51)
'get_table_id:data-center.CFD.CFDATA.CF_OTA_CARTYPE_PART_INFO', (808960, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_SAP_MATERIAL_REPLACE', (809472, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZMDM_CJX', (809984, 51)
'get_table_id:data-center.CFD.CFDATA.CF_OTA_VEHICLE_PART_INFO', (810496, 51)
'get_table_id:data-center.CFD.CFDATA.CF_OTA_VERSION_UPGRADE_CONFIRMATION', (811008, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_SALESPRICETYPE', (811520, 51)
'get_table_id:data-center.CFD.CFHR.DIM_YTHR_EMP', (812032, 51)
'get_table_id:data-center.CFD.CFSAP.DIM_SIZE', (812544, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SRM_PROJECT_SPECIFICPARTS_LIST', (813056, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_SALESGROUP', (813568, 51)
'get_table_id:data-center.CFD.CFDATA.CF_DMS_SJBG', (814080, 51)
'get_table_id:data-center.CFD.CFDATA.ADS_APP_D_CARTYPE', (814592, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_FOREIGN_PART_OUT_SUM', (815104, 51)
'get_table_id:data-center.CFD.CFDATA.CF_MDM_DEALER_STORE_TRY', (815616, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_DMS_XSDDPS', (816128, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_TRANSPORTTYPE', (816640, 51)
'get_table_id:data-center.CFD.CFSRM.SRM_MX_VQA_WASTEQUANTITY', (817152, 51)
'get_table_id:data-center.CFD.CFSAP.DWD_GN_OUT_DETAIL', (817664, 51)
'get_table_id:data-center.CFD.CFFR.FR_FTT_OFFLINE_QUALIFY_RATE', (818176, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_CUSTOMER_PARTNER', (818688, 51)
'get_table_id:data-center.CFD.CFDATA.CF_APP_REPAIR_ORDER_PARTS', (819200, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTSD020', (819712, 51)
'get_table_id:data-center.CFD.CFUIP.ODS_ISOC_ETL_LOG', (820224, 51)
'get_table_id:data-center.CFD.CFHR.DIM_PS_DEPT', (820736, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SRM_COSTSHARE_LIST', (821248, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_CUSTOMER_RATIO', (821760, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_SALESORG', (822272, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SRM_VQA_DESIGNCHANGEDEVELOPMENT', (822784, 51)
'get_table_id:data-center.CFD.CFDATA.CF_YB_CUSTOMER', (823296, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_SALESORDERTYPE', (823808, 51)
'get_table_id:data-center.CFD.CFBPM.DWD_SALE_DOMESTICCUSTOMERREFUNDORDER_CZ', (824320, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_SALESPRICE_TYPE_VC', (824832, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_VEHICLEINFO', (825344, 51)
'get_table_id:data-center.CFD.CFBPM.DIM_DEALER_STORE_CONTACT', (825856, 51)
'get_table_id:data-center.CFD.CFDATA.CF_API_ENGINE_YEAR', (826368, 51)
'get_table_id:data-center.CFD.CFDATA.CF_MDM_DEALER_STORE', (826880, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_SALESPRICE_CUST_VC', (827392, 51)
'get_table_id:data-center.CFD.CFDATA.CF_ZDY_DMS_VEHICLE_BASE', (827904, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SRM_SUPP_PURCPRICE_MATCH_DTL', (828416, 51)
'get_table_id:data-center.CFD.CFDATA.ADS_APP_MIN_BOOKINGORDER', (828928, 51)
'get_table_id:data-center.CFD.CFDATA.DWD_DMS_VEHICLE_SALES_DETAIL', (829440, 51)
'get_table_id:data-center.CFD.CFDATA.CF_OTA_VEHICLE_BASE_INFO', (829952, 51)
'get_table_id:data-center.CFD.CFDMS.DWD_CUSTOMER_ORDER_STATUS', (830464, 51)
'get_table_id:data-center.CFD.CFDATA.CF_OTA_MMI_REPLACE_RECORD', (830976, 51)
'get_table_id:data-center.CFD.CFDATA.CF_APP_REPAIR_ORDER', (831488, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_SALESCHANNEL', (832000, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_SALESDEPT', (832512, 51)
'get_table_id:data-center.CFD.CFHR.DIM_YTHR_DEPT', (833024, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_PRODUCTGROUP', (833536, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_DMS_GWKHSP', (834048, 51)
'get_table_id:data-center.CFD.CFAPP.ODS_V_EXTERNAL_FINANCE_REVIEW', (834560, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_ORDERREASON', (835072, 51)
'get_table_id:data-center.CFD.CFAPP.ODS_V_EXTERNAL_FINANCE_LOAN', (835584, 51)
'get_table_id:data-center.CFD.CFDATA.CF_MDM_VEHICLE_SALESINFO', (836096, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_DMS_GNKHSP', (836608, 51)
'get_table_id:data-center.CFD.CFDATA.CF_MDM_DEALER_MATERIAL_R', (837120, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_DMS_SBJY', (837632, 51)
'get_table_id:data-center.CFD.CFDATA.CF_SAP_ACCOUNTTYPE', (838144, 51)
'get_table_id:data-center.CFD.CFFR.FR_INF_AUTHORITY_REPORT', (838656, 51)
'get_table_id:FR.FRDB.DBO.FR_INF_AUTHORITY_REPORT', (839168, 51)
'get_table_id:data-center.CFD.CFHR.ODS_PS_CF_EMP_FR_VW', (839680, 51)
'get_table_id:FR.FRDB.DBO.ODS_YTHR_T_EMPLOYEE', (840192, 51)
'get_table_id:FR.FRDB.DBO.DIM_PS_EMP', (840704, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T300T', (841216, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_PLAF', (841728, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_CDPOS_I', (842240, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_MATERIAL_INVENTORY', (842752, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_SER03', (843264, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_FR_QIS_JYWCL_GJ_V', (843776, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TKKH2', (844288, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_CSKT', (844800, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ANLZ', (845312, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TVAUT', (845824, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_EQUI', (846336, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_AFVC', (846848, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTMDM_009', (847360, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_CSKS', (847872, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_ZMMT003', (848384, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_AUFK', (848896, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_MBEW_FR', (849408, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TQ30T', (849920, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_VBPA', (850432, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_AFPO', (850944, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_EORD', (851456, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_SKA1', (851968, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T188T', (852480, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_CRCO', (852992, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_LFB1', (853504, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTPP_015C', (854016, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_NSDM_V_MARD', (854528, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ANKT', (855040, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TGSBT', (855552, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTFI013D', (856064, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_PAYEE_FORECURR_SUPPLIER', (856576, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTFI010', (857088, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_KNA1', (857600, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_LAGP', (858112, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZMMT073', (858624, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_RESB', (859136, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTMM_BOM_IDNRK', (859648, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TCDOB', (860160, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_JEST_VBAK', (860672, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_FAGLFLEXT', (861184, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_LIPS', (861696, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_A017', (862208, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_CEPCT', (862720, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_MATE_WERK_TYPE', (863232, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ADR2', (863744, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_PRODUCT_MATERIAL_REL', (864256, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T189T', (864768, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_KONM', (865280, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_DFKKBPTAXNUM', (865792, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_MBEW', (866304, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T301T', (866816, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_QAVE', (867328, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ADRC', (867840, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_LFC3', (868352, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_LFM1', (868864, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_KONH', (869376, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_ENGINE_YEAR_VC', (869888, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_MLDOCCCS_DELTA', (870400, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTFI004', (870912, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T156T', (871424, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_EKET', (871936, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TFKBT', (872448, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_CDHDR', (872960, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TB002', (873472, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_AFKO_TL', (873984, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T025T', (874496, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZMMT_AGE', (875008, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TCURX', (875520, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_CABN', (876032, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_IQC', (876544, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T024F', (877056, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TABWT', (877568, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTFI006', (878080, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T053S', (878592, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ADR6', (879104, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_IBIN', (879616, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T074', (880128, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_FR_PP_IN_DTL_V', (880640, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T001L', (881152, 51)
'get_table_id:data-center.CFD.CFTEST.ODS_SAP_D_V_AFKO_TL', (881664, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_EKKO', (882176, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_SUPPLIER_COMPANY', (882688, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_EKPO', (883200, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZMMT007', (883712, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.LIKP', (884224, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZSDX4', (884736, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T023T', (885248, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_BSEG', (885760, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T001', (886272, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TFKTAXNUMTYPE_T', (886784, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTFI007', (887296, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TVGRT', (887808, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_CEPC', (888320, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TABW', (888832, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ANLA', (889344, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T024E', (889856, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_MATERIAL_FACTORY_BOM', (890368, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_QALS', (890880, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTCO001', (891392, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_CDPOS', (891904, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_KNA1_S', (892416, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_MARA', (892928, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_EQUP', (893440, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_KNVP', (893952, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTSD0083', (894464, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_OUTBUY_PROD_ORDER_FEED_DTL', (894976, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_IBINVALUES', (895488, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T052U', (896000, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TVKBT', (896512, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZMM001', (897024, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T003T', (897536, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_EKKN', (898048, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_BUT000', (898560, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ADR3', (899072, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTMM_BPM_005_N', (899584, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_QMEL', (900096, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_VEHICLE_CONFIG_INFO', (900608, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_KONP', (901120, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T006A', (901632, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZMDM_TJ', (902144, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T005T', (902656, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTFI013', (903168, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_PLAS', (903680, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TVBVK', (904192, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T856T', (904704, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_FICO_ZTFI093B', (905216, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TCURF', (905728, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_LFC1', (906240, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ACESOBJ_ASSGMT', (906752, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_KNC3', (907264, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_AFRU', (907776, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TSPAT', (908288, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ADRP', (908800, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_VBAP', (909312, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_CDHDR_I', (909824, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZMMT012_ZC', (910336, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTFI029', (910848, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T024', (911360, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T001W', (911872, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_KEKO', (912384, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZMMT0021', (912896, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T006T', (913408, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_DD07T', (913920, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_PAYEE_SUPPLIER', (914432, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_APP_VEHICLE_SPECIFICATION', (914944, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T006', (915456, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_RSEG', (915968, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_ADS_APP_MIN_OUTSTOCK', (916480, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_CE1CFMT', (916992, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_CSKU', (917504, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_SKB1', (918016, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTQM009', (918528, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZMMT0064', (919040, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTFI017', (919552, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_MCH1', (920064, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_MATDOC', (920576, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T002', (921088, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TCURR', (921600, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_MLDOC', (922112, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_ACDOCA', (922624, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TVKO', (923136, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TCURT', (923648, 51)
'get_table_id:data-center.CFD.CFTEST.ODS_CEPC', (924160, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_AFVV', (924672, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TBOX_PROD_ORDER_REL_MXG', (925184, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_LFA1', (925696, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_VBRP', (926208, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TVTWT', (926720, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T024D', (927232, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_KNC1', (927744, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTFI018', (928256, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_SER01', (928768, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_AFKO', (929280, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T005U', (929792, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_ZTQM011_ITEM', (930304, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_JEST_AUFK', (930816, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_MAKT', (931328, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_SUPPLIER_BASE', (931840, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_RBKP', (932352, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_KTM_OUT_DETAIL', (932864, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_FRM_FICO_XM_YS_DTL_V', (933376, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_PLPO', (933888, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTFI058', (934400, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TKA02', (934912, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ANLU', (935424, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTPP_015A', (935936, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T134T', (936448, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_KNB1', (936960, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_BP_BANKINFO', (937472, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_QMFE', (937984, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_MAPL', (938496, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTPUB_ZFI027', (939008, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T001K', (939520, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_VBAK', (940032, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_VBFA', (940544, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_OBJK', (941056, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTPP003', (941568, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_STANDARD_VEHICLE_CONFIG_INFO', (942080, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_LIKP', (942592, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_QPCT', (943104, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_QMAT', (943616, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TJ02T', (944128, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_FR_PP_IN_SX_DTL_V', (944640, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T006D', (945152, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TVKOT', (945664, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTMDM_013_F2', (946176, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTLONGTEXT_MARA', (946688, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZPPT005', (947200, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_VBKD', (947712, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_DMS_SJBG_SXRQ', (948224, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_MLDOCCCS', (948736, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_EQUK', (949248, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_KNVV', (949760, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_CKMLHD', (950272, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_A901', (950784, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_USR21', (951296, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_ZTQM011_HEAD', (951808, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TVSBT', (952320, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTFI009', (952832, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_MARC', (953344, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_BUT0BK', (953856, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZSDX5', (954368, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTLONGTEXT_SALES', (954880, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZPPTCO02', (955392, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_BKPF', (955904, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_SKAT', (956416, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_ZMMT_BC_38', (956928, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZMARA', (957440, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TVKGR', (957952, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_VBRK', (958464, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZMMT012_FICO', (958976, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_TVAKT', (959488, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZMMT_AGE_FR', (960000, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_IBSYMBOL', (960512, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZMIGO_BADI_SRM01', (961024, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTQM011_JYY', (961536, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_T030', (962048, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.KNMT', (962560, 51)
'get_table_id:bpm.CFDW.DBO.KNMT', (963072, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZMMT_BC_19', (963584, 51)
'get_table_id:bpm.CFDW.DBO.T_SAP_BARCODEINFO_UNBIND', (964096, 51)
'get_table_id:bpm.CFDW.DBO.CF_EAM_ASSET', (964608, 51)
'get_table_id:bpm.CFDW.DBO.T_SAP_BARCODEINFO', (965120, 51)
'get_table_id:bpm.CFDW.DBO.EKKN', (965632, 51)
'get_table_id:bpm.CFDW.DBO.ODS_V_EKKO', (966144, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_DMS_CDHZ', (966656, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_MATERIAL_GROUP', (967168, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_VEHICLEINFO_TMP', (967680, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_VEHICLEINFO_TMP1', (968192, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.ZEEHO_AFTERMARKET_BOM', (968704, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.CF_SAP_MATERIAL_TYPE', (969216, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_DEALER_STORE_CONTACT_TYPE', (969728, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_SELFTOOL', (970240, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_GY_SBJY_S', (970752, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CONTRACT_ENGINE_YEAR', (971264, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_FREEDOCUMENTENDORSEMENT_P_D1', (971776, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_FIN_ZZJBZGS_1', (972288, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_ZZJBZGS_1', (972800, 51)
'get_table_id:data-center.CFD.CFBPM.TMP_MX_BPMINSTPROCSTEPS', (973312, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_APPLICATION', (973824, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZLL_VEHICLESTATE_CONFIRM_STATE', (974336, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_PRODUCTIONSITESTOPLINEFEEDBACK_P', (974848, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_BUDGETADJUST', (975360, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZLL_VEHICLESTATE_STATEITEM', (975872, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_DMS_SBSP_CLAIM_GW_P_D', (976384, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASE_P', (976896, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_MK_SBTJ', (977408, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_T_VEHICLEINFO_FORCARONLINE', (977920, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERKU', (978432, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_ORDERREVIEW_P_D_0', (978944, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_GY_SBJY_OLD', (979456, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPCHECKANDACCEPT_P', (979968, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_COMPONENTFEEDQUALITYPROBLEMTRACKING_P', (980480, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_BANKRECEIPT_P_D', (980992, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_PM_EQUIPMENTTRAINING', (981504, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPCHECKANDACCEPT_P_DJSWL', (982016, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_FREEDOCUMENTENDORSEMENT_P', (982528, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VOA_DESIGNCHANGEMANAGEMENT_P_D_SJBGLL', (983040, 51)
'get_table_id:bpm.MXBPMDATA.DBO.CF_SAP_FICO_FKSQ_LX', (983552, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_CF_SAP_FICO_FKSQ_LX', (984064, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_MK_GNKHSP', (984576, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SBSP_DRAWMONEY', (985088, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_IT_REQUIREMENTSFORBPM_P', (985600, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_CONTRACTAPPROVALAPPLICATION_P', (986112, 51)
'get_table_id:data-center.CFD.CFK3.ODS_T_SYNC_VEHICLECERTIFICATION', (986624, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_FIN_BANKRECEIPT_BALANCE', (987136, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPCONSTRUCTION_P', (987648, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_KTM_SAP_MDM_MATERIALCOLLAR_P_D', (988160, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_ZL_SCGZTZXZ_V_BI', (988672, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_CW_BGINVOICE_S', (989184, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASEORDER_P_D', (989696, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_NEWPRODUCTDEVELOPMENTAGREEMENT_P', (990208, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_T_CF_CHECKRIGHT_FEETYPE', (990720, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_KU_DEALER_STORE_CONTACT_S', (991232, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_REPAIRROOM_CHECKANDACCEPT_P', (991744, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_REPAIRROOM_CONSTRUCTION_P', (992256, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_U_ZLL_WASTEQUANTITYENTRY', (992768, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_DESIGNCHANGESTRATEGY_P', (993280, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_GY_SBJY_P', (993792, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_SBSCRAPDISPOSE_P', (994304, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_DESIGNCHANGESTRATEGY_P_D', (994816, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_ORIGINALPARTS_P', (995328, 51)
'get_table_id:data-center.CFD.CFK3.ODS_T_VEHICLEINFO', (995840, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZLL_VEHICLESTATE_CONFIGTYPE', (996352, 51)
'get_table_id:data-center.CFD.CFTEST.ODS_CF_D_T_ERP_ICITEM', (996864, 51)
'get_table_id:bpm.BPMDB.DBO.CF_MENU_PERMISSION_SECURITY', (997376, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_MENU_PERMISSION_SECURITY', (997888, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SEP_SAFETYRECTIFICATIONMULTI_P', (998400, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_IT_REQUIREMENTSFORSAP_P', (998912, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPMATERALAPPLY_P', (999424, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_DESIGNCHANGEDEVELOPMENT_P_D', (999936, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_JS_XPSZCJH', (1000448, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_SELFTOOL_D_P', (1000960, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_DMS_SBSP_CLAIM_GN_P_D_ITEM', (1001472, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_GY_SBJY_P_D_BM_ZZ', (1001984, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_INSPECTIONEXEMPTIONSUPPLIER_P', (1002496, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_BUDGETADJUST_1', (1003008, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_QUALITYREPLAYCE', (1003520, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_SPOTQUALITYDISPOSE_P_NEW', (1004032, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_KTM_V_BPMINSTTASKS', (1004544, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_GY_SUPPLIERPROCUREMENTPRICEMATCHING', (1005056, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_BPMSYSOUMEMBERS', (1005568, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_MK_WLLY', (1006080, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_PLZLSG_2', (1006592, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_BANKRECEIPT_P', (1007104, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_SIPU_SEND', (1007616, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_RPHTSP_1', (1008128, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_SPOTQUALITYDISPOSE_WLINFO', (1008640, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_VQA_WASTEQUANTITYENTRY', (1009152, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_BPMINSTTASKS', (1009664, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_PM_CFMKDREISSUE', (1010176, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_RDC_OTAVERSIONUPGRADECONFIRMATION_P', (1010688, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_DECLARATE_P_D', (1011200, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_GY_SBJY_P_D', (1011712, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_NEWPRODUCTMOULDINSTRUCTION_P_D', (1012224, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_FREEDOCUMENTENDORSEMENT_P_D2', (1012736, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_FEEDBACK_P', (1013248, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_RPLBJDY', (1013760, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_SBSCRAPDISPOSE_P_D', (1014272, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_DESIGNCHANGEDEVELOPMENT_P', (1014784, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_HR_PERFORMANCEAPPRAISAL_PS', (1015296, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_PLZLSG_1', (1015808, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SAP_FICO_FKSQ_LX', (1016320, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_FIN_SHARE_P', (1016832, 51)
'get_table_id:data-center.CFD.CFBPM.TMP_BPMINSTTASKS', (1017344, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_NEWPRODUCTDEVELOPMENTAGREEMENT_P_D_BC_C', (1017856, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_RPLBJDD', (1018368, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_DMS_SBSP_CLAIM_GW_P', (1018880, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_REPAIRROOM_DISIGN_P', (1019392, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_GY_SUPPLIERPROCUREMENTPRICEMATCHING_D', (1019904, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VOA_DESIGNCHANGEMANAGEMENT_P', (1020416, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_GENERATIVEDISPATCH_P', (1020928, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_FXSCPZY', (1021440, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SH_HTSPSQ', (1021952, 51)
'get_table_id:data-center.CFD.CFBPM.TMP_MX_BPMINSTTASKS', (1022464, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ADM_AIXINFUND', (1022976, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_FREEDOCUMENTENDORSEMENT_P_D', (1023488, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_DMS_SBSP_CLAIM_GN_P', (1024000, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_ADM_DOCUMENTENDORSEMENT_P', (1024512, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_ORDERREVIEW_P_D_1', (1025024, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_HR_LEFTOVER_PS', (1025536, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_CONFIRMATIONSWITCHINGINFORMATION_P', (1026048, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASEORDER_P', (1026560, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VOA_DESIGNCHANGEMANAGEMENT_P_D_SZBG', (1027072, 51)
'get_table_id:bpm.MXBPMDATA.DBO.MX_SALE_PURCHASEREQUISITIONS_P_D', (1027584, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_SALE_PURCHASEREQUISITIONS_P_D', (1028096, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CQ_BPMINSTPROCSTEPS', (1028608, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_DMS_SBSP_CLAIM_GN_P_D', (1029120, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VEN_BD_PURCHASECATEGORYDIVISIONLIBRARY', (1029632, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VOA_DESIGNCHANGEMANAGEMENT_P_D', (1030144, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_RPHTSP', (1030656, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_WGCCGSQ', (1031168, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VOA_DESIGNCHANGEMANAGEMENT_P_D_SJBGSL', (1031680, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_DECLARATE_P', (1032192, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_JS_PROJECTMEMBERLIST', (1032704, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_GLFJL', (1033216, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_SPOTQUALITYDISPOSE_P', (1033728, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_MK_SDLDQR', (1034240, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_PM_CFMKDREISSUE_D', (1034752, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_WGCCGSQ_1', (1035264, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZLL_VEHICLESTATE_CONFIRM', (1035776, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_VQA_WASTEQUANTITY', (1036288, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VOA_DESIGNCHANGEMANAGEMENT_P_D_SJBGFDJ', (1036800, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SEP_SAFETYRECTIFICATIONMULTI_P_D', (1037312, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_RPLBJDD_3', (1037824, 51)
'get_table_id:bpm.BPMDB.DBO.CF_IT_PERMISSIONOPEN_NV_ADGROUP', (1038336, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_SUPPLIERDEVELOP_BASEDATA', (1038848, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_RPLBJDY_1', (1039360, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_T_CF_RDC_PROJECTSPECIFICPARTSLIST', (1039872, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_BPMINSTTASKS', (1040384, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_MK_GXWLLY', (1040896, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_BPMSYSUSERS', (1041408, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_GYTEMPORARY_P', (1041920, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_SELFTOOL_D', (1042432, 51)
'get_table_id:data-center.CFD.CFBPM.TMP_BPMINSTPROCSTEPS', (1042944, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_COMPONENTVERIFICATE_P', (1043456, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_JS_XPSZCJH_1', (1043968, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_VQA_VEHICLEEVALUATIONINFORMATIONCFMCFPA_P', (1044480, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_PLZLSG_3', (1044992, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_U_ZLL_WASTEQUANTITY', (1045504, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPMATERALAPPLY_P_D1', (1046016, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZLL_VEHICLESTATE_CONFIGITEM', (1046528, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_RETURNOFFULLVEHICLEORDERSONLINE', (1047040, 51)
'get_table_id:bpm.MXBPMDATA.DBO.MX_SALE_PURCHASEREQUISITIONS_P', (1047552, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_SALE_PURCHASEREQUISITIONS_P', (1048064, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_V_BPMINSTPROCSTEPS', (1048576, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_FIN_BANKRECEIPT_UNDECLAREBALANCE', (1049088, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_RDC_NEWPRODUCTPROJECTAPPROVAL_P', (1049600, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SAP_MDM_WLXZXG_MX', (1050112, 51)
'get_table_id:data-center.CFD.CFBPM.TMP_KTM_BPMINSTTASKS', (1050624, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_T_SAP_PAYMENTNATURE', (1051136, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_SUPPLIERMOULD_P', (1051648, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPKU', (1052160, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASEAPPLICATION_P_D', (1052672, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPCHECKANDACCEPT_P_DSTORE', (1053184, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CQ_BPMINSTTASKS', (1053696, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_BANKADJUST_P', (1054208, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_GY_SBJY_NEW', (1054720, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_RFVORDERSONLINE_1', (1055232, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_BANKRECEIPTKU_P', (1055744, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_SUPPLIERMOULD_P_D', (1056256, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SAP_SD_XSDDBG', (1056768, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_KU_DEALER_STORE_S', (1057280, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERNEWACCOUNTAPPLICATION_P', (1057792, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_NEWPRODUCTMOULDINSTRUCTION_P', (1058304, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_HR_YLZ', (1058816, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_DMS_SJBG', (1059328, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_DMS_SBSP_CLAIM_GN_P', (1059840, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASE_P_D', (1060352, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_FR_PP_WLLY', (1060864, 51)
'get_table_id:data-center.CFD.CFBPM.TMP_DWD_PURCH_H_CURPURPRICE', (1061376, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_MATERIALCOLLAR_P', (1061888, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_GY_SBJY_P_D_BM', (1062400, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_V_BPMINSTTASKS', (1062912, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_ZEEHODEALERSHOPMATERALAPPLY_P_D2', (1063424, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_NEWPRODUCTDEVELOPMENTAGREEMENT_P_D_BC', (1063936, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_PLZLSG', (1064448, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SAP_FICO_FKSQ_LX_WB_GJSZCODEMENU', (1064960, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_BPMSYSOUMEMBERS', (1065472, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_BPMSYSOUS', (1065984, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASEAPPLICATION_P', (1066496, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_FIN_ZZJBZGS', (1067008, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_ZZJBZGS', (1067520, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_KTM_SAP_MDM_MATERIALCOLLAR_P', (1068032, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_DISPOSE', (1068544, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_DEALER_TRY_S', (1069056, 51)
'get_table_id:bpm.MXBPMDATA.DBO.MX_SALE_PURCHASEORDERISSUANCE_P', (1069568, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_SALE_PURCHASEORDERISSUANCE_P', (1070080, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_BANKADJUST_P_D', (1070592, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_ORDERREVIEW_P', (1071104, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_BPMSYSOUS', (1071616, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_SALE_OPENACCOUNT', (1072128, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_MATERIALCOLLAR_P_D', (1072640, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SAP_SD_XSDDBG_1', (1073152, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_MX_BPMSYSUSERS', (1073664, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_HR_LEFTOVER_P', (1074176, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_EAM_ASSETSFINISHACCEPTANCE', (1074688, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_HR_CHANGEDEPTPO_PS', (1075200, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_SIPU', (1075712, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_GYTEMPORARY_P_D_0', (1076224, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_NEWPRODUCTPROJECTAPPROVAL_P', (1076736, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_GY_SBJY', (1077248, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_V_CF_KU_DEALER_STORE_TRY_S', (1077760, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_GYTEMPORARY_P_D', (1078272, 51)
'get_table_id:bpm.CFMOTO.DBO.V_VEHICLEINFO_2021', (1078784, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SAP_TRANSPORTTYPE', (1079296, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BA_UUID', (1079808, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_CLAIM_CHECK_LOG', (1080320, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_TM_ORG', (1080832, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BA_TROUBLE', (1081344, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_WITHDRAW_DTL', (1081856, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_V_MATERIAL_HSC_S', (1082368, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_ACTIVITY_VEHICLE', (1082880, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_ACTIVITY', (1083392, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_PT_BA_PART_REPLACE', (1083904, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_CLAIM_OTHER_FEE', (1084416, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_PT_BA_CHART_ASSEMBLY_PART', (1084928, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_BOOKING_ORDER', (1085440, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_PT_BU_ORDER_DTL', (1085952, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_TM_VEHICLE', (1086464, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SAP_SALESPRICE_TYPE', (1086976, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_WITHDRAW', (1087488, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_ASSEMBLY_INFORMATION', (1088000, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_REPAIR_ORDER_WORKHOURS', (1088512, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BA_OTHER_COST', (1089024, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_VS_BA_MATERIAL', (1089536, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_PT_BU_ORDER', (1090048, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_REPAIR_ORDER', (1090560, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_CLAIM_PART', (1091072, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_PT_BA_CHART_ASSEMBLY', (1091584, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_TM_DEALER', (1092096, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_V_SPD', (1092608, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_PT_BA_INFO', (1093120, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_VS_BU_CUSTOMER_ORDER', (1093632, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_REPAIR_ORDER_PARTS', (1094144, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_V_SPD_S', (1094656, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_V_SPD_DETAIL', (1095168, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_TM_CUSTOMER', (1095680, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_CKD_UPDATE', (1096192, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_TM_DIVISION', (1096704, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_VS_BA_COLOR', (1097216, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_DIC_TREE', (1097728, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_V_VEHICLEREGISTRATION', (1098240, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_CLAIM', (1098752, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_PT_BA_CHART_CATALOG', (1099264, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_V_DEALER_MATERIAL_S', (1099776, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.UIP_MMI_DEVICE', (1100288, 51)
'get_table_id:data-center.CFD.CFUIP.ODS_UIP_MMI_DEVICE', (1100800, 51)
'get_table_id:mid_cfdata.CFDATA.CFDATA.ISOC_APT_OFFICE_LOG', (1101312, 51)
'get_table_id:data-center.CFD.CFUIP.ODS_ISOC_APT_OFFICE_LOG_S', (1101824, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_BARCODEINFO_TMP', (1102336, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_SALES_BASE_DEPTAREA_R', (1102848, 51)
'get_table_id:data-center.CFD.CFFR.FR_PP_BASE_DEPT_COEFFICIENT_A', (1103360, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FRM_SD_FOUR_CFMOTO', (1103872, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_SALES_BASE_DEPARTMENT_LIST', (1104384, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FRM_SD_USD_LS_DTL', (1104896, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FRM_SD_STOCK_TYPE', (1105408, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_SALES_BASE_PRODUCTTYPE', (1105920, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_TAXRATIO', (1106432, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_SALES_BASE_AUTHORIZATION', (1106944, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_SALES_BASE_DEPTAREA_D', (1107456, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_FEEDING_CP_V', (1107968, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_SALES_K3_DELIVERYDETAILS', (1108480, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_SALES_BASE_AREATYPE', (1108992, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_SALES_BASE_DEPARTMENT', (1109504, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_FEEDING_DTL_V', (1110016, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_DZ_CUSTOMER_V', (1110528, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_SALES_K3_CUSTPLUSINFO', (1111040, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_V_FRM_SD_FOUR_YEAR_TOTAL', (1111552, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_SALES_BASE_YEARPLAN', (1112064, 51)
'get_table_id:data-center.CFD.CFFR.FR_PP_BASE_COEFFICIENT', (1112576, 51)
'get_table_id:data-center.CFD.CFFR.FR_PP_BASE_COEFFICIENT_HR', (1113088, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_SAP_ADJUST', (1113600, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_SALES_BASE_CARTYPEINFO', (1114112, 51)
'get_table_id:data-center.CFD.CFAPS.ODS_CF_TARZAN_APS_MLTP_HPS_DOMESTIC_FORECAST', (1114624, 51)
'get_table_id:data-center.CFD.CFAPS.ODS_CF_TARZAN_APS_MT_DEMAND_ORDER', (1115136, 51)
'get_table_id:data-center.CFD.CFAPS.ODS_HPS_ROUGH_SPLIT_RULES', (1115648, 51)
'get_table_id:data-center.CFD.CFAPS.ODS_CF_TARZAN_APS_MLTP_YP_MONWEEK_PLAN_HIS', (1116160, 51)
'get_table_id:data-center.CFD.CFAPS.ODS_CF_TARZAN_APS_MT_MAKE_ORDER', (1116672, 51)
'get_table_id:data-center.CFD.CFAPS.ODS_CF_TARZAN_APS_MLTP_YP_PLAN_VERSION', (1117184, 51)
'get_table_id:FR.FRDB.DBO.INF_ROLE_ENTRY', (1117696, 51)
'get_table_id:data-center.CFD.CFFR.ODS_INF_ROLE_ENTRY', (1118208, 51)
'get_table_id:FR.FRDB.DBO.FR_REPORT_MANAGEMENT_DETAIL_V', (1118720, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_REPORT_MANAGEMENT_DETAIL_V', (1119232, 51)
'get_table_id:FR.FRDB.DBO.INF_USER_ENTRY', (1119744, 51)
'get_table_id:data-center.CFD.CFFR.ODS_INF_USER_ENTRY', (1120256, 51)
'get_table_id:FR.FRDB.DBO.FR_LOGDB_TYPE', (1120768, 51)
'get_table_id:data-center.CFD.CFFR.ODS_FR_LOGDB_TYPE', (1121280, 51)
'get_table_id:FR.FRDB.DBO.V_YTHR_T_ATTRECORD', (1121792, 51)
'get_table_id:data-center.CFD.CFHR.ODS_YTHR_T_ATTRECORD', (1122304, 51)
'get_table_id:FR.FRDB.DBO.FRM_HR_LZL_V', (1122816, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.ODS_V_MATERIAL', (1123328, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.ODS_V_MATERIAL_FACTORY', (1123840, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SRM_PROJECT_MEMBER_LIST', (1124352, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_SIZE', (1124864, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_SALESORDERTYPE', (1125376, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_VEHICLEINFO', (1125888, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_ACCOUNTTYPE', (1126400, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_SALESPRICE_COMPANY', (1126912, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_HR_DEPT', (1127424, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_APP_CP_CARTYPE_REL', (1127936, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.ADS_APP_MIN_BOOKINGORDER', (1128448, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_HR_EMP', (1128960, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.ADS_APP_D_TBOXCHANGELIST', (1129472, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.ADS_APP_D_CARTYPE', (1129984, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_APP_REPAIR_ORDER', (1130496, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.ISOC_ETL_LOG', (1131008, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_APP_REPAIR_ORDER_PARTS', (1131520, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_MDM_DEALER_MATERIAL', (1132032, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_ODS_FRM_DMS_LS_V', (1132544, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DIM_SAP_H_CUSTOMER_RATIO', (1133056, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_BPM_SALE_DOMESTICCUSTOMERREFUNDORDER_CZ', (1133568, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_APP_EV_ODS_FDDEF_APP', (1134080, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_SALESPRICETYPE', (1134592, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.TEST_ZJF', (1135104, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.ADS_APP_MIN_OUTSTOCK', (1135616, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_OTA_VERSION_UPGRADE_CONFIRMATION', (1136128, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_ZDY_DMS_VEHICLE_BASE', (1136640, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_PURCHASEGROUP', (1137152, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.SRM_MX_VQA_WASTEQUANTITY', (1137664, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_DMS_SJBG', (1138176, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_MATERIAL_VC', (1138688, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_CUSTOMER_BASE', (1139200, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DWD_SALES_H_SALESPRICE_CUST', (1139712, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_ODS_V_EXTERNAL_FINANCE_REVIEW', (1140224, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_OTA_MMI_REPLACE_RECORD', (1140736, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DIM_SAP_D_COSTCENTER', (1141248, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_TRANSPORTTYPE', (1141760, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_DMS_GWKHSP', (1142272, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DIM_SAP_H_CUSTOMER_STORE', (1142784, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SRM_COSTSHARE_LIST', (1143296, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DWD_SALES_H_SALESPRICE_TYPE', (1143808, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DIM_SAP_H_CUSTOMER_PARTNER', (1144320, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DIM_SAP_MATERIAL_REPLACE', (1144832, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_APP_TBOX_CARTYPE_REL2', (1145344, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DIM_SAP_H_CUSTOMER', (1145856, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DIM_SAP_H_CUSTOMER_SALES', (1146368, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SRM_VQA_DESIGNCHANGEDEVELOPMENT', (1146880, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DIM_SAP_H_MATERIAL', (1147392, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_DMS_GNKHFKSQ', (1147904, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_TWO_FTT_OFFLINE_QUALIFY_RATE', (1148416, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_OTA_CARTYPE_PART_INFO', (1148928, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_PRODUCTGROUP', (1149440, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_MATERIAL_FACTORY', (1149952, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_YB_CUSTOMER', (1150464, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_APP_VEHICLE_SPECIFICATION', (1150976, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DIM_COM_CHINA_AREA_GD', (1151488, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_OTA_VEHICLE_BASE_INFO', (1152000, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_COUNTRY', (1152512, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_MDM_ZMDM_CJX', (1153024, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_UNIT', (1153536, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.ODS_SAP_MIN_V_INNERORDER', (1154048, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_ODS_V_EXTERNAL_FINANCE_LOAN', (1154560, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DWD_SALES_H_SALESPRICE_CUST_VC', (1155072, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DIM_SAP_H_SUPPLIER', (1155584, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_OTA_VEHICLE_PART_INFO', (1156096, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SRM_SUPP_PURCPRICE_MATCH_DTL', (1156608, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_FOREIGNPART_OUT_SUM', (1157120, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_MDM_DEALER_STORE_CONTACT', (1157632, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_DMS_GNKHSP', (1158144, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_DMS_CUSTOMER_ORDER_STATUS', (1158656, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_POSITION', (1159168, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_DMS_XSDDPS', (1159680, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_MATERIAL_EX', (1160192, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_SALESORG', (1160704, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_SALESGROUP', (1161216, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_APP_VEHICLE_SALES_DETAIL', (1161728, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_ODS_GN_OUT_DETAIL', (1162240, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_BANKACCOUNT', (1162752, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_ODS_V_EXTERNAL_FINANCE_INSURANCE', (1163264, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_MDM_DEALER_STORE_TRY', (1163776, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_APP_TBOX_CARTYPE_REL', (1164288, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SRM_PROJECT_SPECIFICPARTS_LIST', (1164800, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DWD_SALES_H_SALESPRICE_TYPE_VC', (1165312, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_MDM_DEALER_STORE', (1165824, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_MDM_VEHICLE_SALESINFO', (1166336, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_DMS_GWKHTK', (1166848, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_SALESDEPT', (1167360, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_DMS_SBJY', (1167872, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_SALESCHANNEL', (1168384, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_APP_TBOX_CARTYPE_REL_20240314', (1168896, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_APP_REPAIR_ORDER_WORKHOURS', (1169408, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.DIM_SAP_H_CUSTOMER_ADDRESS', (1169920, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_DMS_FERTH_VSBED', (1170432, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_ORDERREASON', (1170944, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.PT_BA_CHART_ASSEMBLY', (1171456, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.V_DEALER_MATERIAL', (1171968, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_ACTIVITY', (1172480, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_CLAIM_PART', (1172992, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.VS_BU_CUSTOMER_ORDER', (1173504, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.VS_BA_COLOR', (1174016, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.V_MATERIAL_HSC', (1174528, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_ASSEMBLY_INFORMATION', (1175040, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BA_UUID', (1175552, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.PT_BU_ORDER', (1176064, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.TM_DEALER', (1176576, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.TM_VEHICLE', (1177088, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.PT_BA_PART_REPLACE', (1177600, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_REPAIR_ORDER', (1178112, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.V_VEHICLEREGISTRATION', (1178624, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.PT_BA_CHART_CATALOG', (1179136, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SAP_SALESPRICE_TYPE', (1179648, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_CLAIM_CHECK_LOG', (1180160, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.TM_DIVISION', (1180672, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_CLAIM_OTHER_FEE', (1181184, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_WITHDRAW', (1181696, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.PT_BA_CHART_ASSEMBLY_PART', (1182208, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.PT_BA_INFO', (1182720, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_BOOKING_ORDER', (1183232, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.TM_CUSTOMER', (1183744, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.VS_BA_MATERIAL', (1184256, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BA_TROUBLE', (1184768, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_CLAIM', (1185280, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.TM_ORG', (1185792, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.V_SPD', (1186304, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_WITHDRAW_DTL', (1186816, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SAP_TRANSPORTTYPE', (1187328, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BA_OTHER_COST', (1187840, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.CKD_UPDATE', (1188352, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_REPAIR_ORDER_PARTS', (1188864, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_REPAIR_ORDER_WORKHOURS', (1189376, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.V_SPD_DETAIL', (1189888, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.V_KDVEHICLEINFO', (1190400, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.DIC_TREE', (1190912, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.PT_BU_ORDER_DTL', (1191424, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_ACTIVITY_VEHICLE', (1191936, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.ISOC_APT_OFFICE_LOG', (1192448, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.UIP_MMI_DEVICE', (1192960, 51)
'get_table_id:cfdms.DEFAULT.CFMOTO.V_TJ_SC_REL', (1193472, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_V_TJ_SC_REL', (1193984, 51)
'get_table_id:cfdms.DEFAULT.CFMOTO.FR_DMS_QIS_SP_V', (1194496, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_FR_DMS_QIS_SP_V', (1195008, 51)
'get_table_id:cfdms.DEFAULT.CFMOTO.FRM_DMS_LS_V', (1195520, 51)
'get_table_id:cfdms.DEFAULT.CFMOTO.FR_ZEEHO_APPOINTMENT_INFORMATION', (1196032, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_FR_ZEEHO_APPOINTMENT_INFORMATION', (1196544, 51)
'get_table_id:cfdms.DEFAULT.CFMOTO.FR_ZEEHO_ORDER', (1197056, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_FR_ZEEHO_ORDER', (1197568, 51)
'get_table_id:cfdms.DEFAULT.CFMOTO.FRM_DMS_USD_LS_V', (1198080, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_FRM_DMS_USD_LS_V', (1198592, 51)
'get_table_id:cfdms.DEFAULT.CFMOTO.V_VIN_MMI', (1199104, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_V_VIN_MMI_S', (1199616, 51)
'get_table_id:cfdms.DEFAULT.CFMOTO.V_VEHICLE_SALES_DETAIL', (1200128, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_V_VEHICLE_SALES_DETAIL', (1200640, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_V_VIN_MMI', (1201152, 51)
'get_table_id:qis.QIS.PUBLIC.FRM_DIM_DATEYMD', (1201664, 51)
'get_table_id:qis.QIS.PUBLIC.ODS_SAP_ZMMT_BC_19', (1202176, 51)
'get_table_id:qis.QIS.PUBLIC.ODS_SAP_ZMMT_BC_18', (1202688, 51)
'get_table_id:qis.QIS.PUBLIC.FRM_DATEYM', (1203200, 51)
'get_table_id:APSPRD.DEFAULT.TARZAN_APS_MLTP.HPS_ROUGH_SPLIT_RULES', (1203712, 51)
'get_table_id:APSPRD.DEFAULT.TARZAN_APS_MLTP.YP_MONWEEK_PLAN_HIS', (1204224, 51)
'get_table_id:APSPRD.DEFAULT.TARZAN_APS.MT_MAKE_ORDER', (1204736, 51)
'get_table_id:APSPRD.DEFAULT.TARZAN_APS_MLTP.HPS_DOMESTIC_FORECAST', (1205248, 51)
'get_table_id:APSPRD.DEFAULT.TARZAN_APS.MT_DEMAND_ORDER', (1205760, 51)
'get_table_id:APSPRD.DEFAULT.TARZAN_APS_MLTP.YP_PLAN_VERSION', (1206272, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_VEHICLEINFO_TMP1', (1206784, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.ZEEHO_AFTERMARKET_BOM', (1207296, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_MATERIAL_GROUP', (1207808, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_VEHICLEINFO_TMP', (1208320, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_SAP_MATERIAL_TYPE', (1208832, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_DMS_CDHZ', (1209344, 51)
'get_table_id:qis.QIS.PUBLIC.FAULT_INFORMATION', (1209856, 51)
'get_table_id:qis.QIS.PUBLIC.FTT_CX', (1210368, 51)
'get_table_id:qis.QIS.PUBLIC.FTT_GATHER', (1210880, 51)
'get_table_id:qis.QIS.PUBLIC.VEHICLE_PROPERTIES', (1211392, 51)
'get_table_id:qis.QIS.PUBLIC.ZYRY_INFORMATION', (1211904, 51)
'get_table_id:qis.QIS.PUBLIC.CUSTOMER_COMPLAINTS', (1212416, 51)
'get_table_id:qis.QIS.PUBLIC.PRODUCT_EVALUATION_PROBLEM', (1212928, 51)
'get_table_id:qis.QIS.PUBLIC.FTT_CJH_CARTYPE', (1213440, 51)
'get_table_id:qis.QIS.PUBLIC.MATERIEL_INFORMATION', (1213952, 51)
'get_table_id:qis.QIS.PUBLIC.FTT_GX', (1214464, 51)
'get_table_id:qis.QIS.PUBLIC.ZJY_INFORMATION', (1214976, 51)
'get_table_id:qis.QIS.PUBLIC.V_CARTYPE', (1215488, 51)
'get_table_id:cfdms.DEFAULT.CFMOTO.FRM_DMS_ZCSTOCK_V', (1216000, 51)
'get_table_id:cfdms.DEFAULT.CFMOTO.FRM_DMS_PO_V', (1216512, 51)
'get_table_id:cfdms.DEFAULT.CFMOTO.FRM_DMS_GXPSTOCK_V', (1217024, 51)
'get_table_id:SRM.DEFAULT.SRM.SPRM_PR_HEADER', (1217536, 51)
'get_table_id:SRM.DEFAULT.SRM.SSTA_SETTLE_LINE', (1218048, 51)
'get_table_id:SRM.DEFAULT.SRM.HPFM_TENANT', (1218560, 51)
'get_table_id:SRM.DEFAULT.SRM.SSLM_SUPPLIER_BASIC', (1219072, 51)
'get_table_id:SRM.DEFAULT.SRM.HPFM_OPERATION_UNIT', (1219584, 51)
'get_table_id:SRM.DEFAULT.SRM.HPFM_COMPANY', (1220096, 51)
'get_table_id:SRM.DEFAULT.SRM.SSTA_TAX_INVOICE_HEADER', (1220608, 51)
'get_table_id:SRM.DEFAULT.SRM.SMDM_ITEM_CATEGORY', (1221120, 51)
'get_table_id:SRM.DEFAULT.SRM.IAM_USER', (1221632, 51)
'get_table_id:SRM.DEFAULT.SRM.SPCM_PC_TABLE_EXTEND', (1222144, 51)
'get_table_id:SRM.DEFAULT.SRM.SSTA_SETTLE', (1222656, 51)
'get_table_id:SRM.DEFAULT.SRM.SSLM_SUPPLIER_CATEGORY', (1223168, 51)
'get_table_id:SRM.DEFAULT.SRM.SQAM_CLAIM_TYPE', (1223680, 51)
'get_table_id:SRM.DEFAULT.SRM.SPRM_PR_LINE', (1224192, 51)
'get_table_id:SRM.DEFAULT.SRM.HPFM_EMPLOYEE_USER', (1224704, 51)
'get_table_id:SRM.DEFAULT.SRM.HPFM_LOV_VALUE', (1225216, 51)
'get_table_id:SRM.DEFAULT.SRM.SPCM_PC_HEADER', (1225728, 51)
'get_table_id:SRM.DEFAULT.SRM.SPCM_PC_STAGE', (1226240, 51)
'get_table_id:SRM.DEFAULT.SRM.SSTA_CHARGE_HEADER', (1226752, 51)
'get_table_id:SRM.DEFAULT.SRM.SPCM_PC_SUBJECT', (1227264, 51)
'get_table_id:SRM.DEFAULT.SRM.SMDM_COST_CENTER', (1227776, 51)
'get_table_id:SRM.DEFAULT.SRM.ACT_HI_PROCINST', (1228288, 51)
'get_table_id:SRM.DEFAULT.SRM.SODR_PO_HEADER', (1228800, 51)
'get_table_id:SRM.DEFAULT.SRM.SQAM_ED_PROBLEM_HEADER', (1229312, 51)
'get_table_id:SRM.DEFAULT.SRM.ACT_HI_TASKINST', (1229824, 51)
'get_table_id:SRM.DEFAULT.SRM.ACT_EXCEPTION', (1230336, 51)
'get_table_id:SRM.DEFAULT.SRM.SQAM_CLAIM_FORM_LINE', (1230848, 51)
'get_table_id:SRM.DEFAULT.SRM.SSLM_SUPPLIER_SYNC', (1231360, 51)
'get_table_id:SRM.DEFAULT.SRM.ACT_HI_COMMENT', (1231872, 51)
'get_table_id:SRM.DEFAULT.SRM.HPFM_TAX', (1232384, 51)
'get_table_id:SRM.DEFAULT.SRM.SPCM_PC_TYPE', (1232896, 51)
'get_table_id:SRM.DEFAULT.SRM.HPFM_EMPLOYEE', (1233408, 51)
'get_table_id:SRM.DEFAULT.SRM.ACT_RE_PROCDEF', (1233920, 51)
'get_table_id:SRM.DEFAULT.SRM.SODR_PO_LINE', (1234432, 51)
'get_table_id:SRM.DEFAULT.SRM.SPFM_PARTNER', (1234944, 51)
'get_table_id:SRM.DEFAULT.SRM.SQAM_CLAIM_FORM_HEADER', (1235456, 51)
'get_table_id:SRM.DEFAULT.SRM.SSTA_CHARGE_LINE', (1235968, 51)
'get_table_id:SRM.DEFAULT.SRM.SSTA_SETTLE_HEADER', (1236480, 51)
'get_table_id:SRM.DEFAULT.SRM.SODR_ORDER_TYPE', (1236992, 51)
'get_table_id:SRM.DEFAULT.SRM.HPFM_UNIT', (1237504, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.CFDL_CL_TMP', (1238016, 51)
'get_table_id:bc.DEFAULT.CFMOTO.CF_KTM_RECEIVING_ORDER', (1238528, 51)
'get_table_id:bc.DEFAULT.CFMOTO.CF_ASSEMBLY_PLAN', (1239040, 51)
'get_table_id:bc.DEFAULT.CFMOTO.CF_BARCODE_BIND', (1239552, 51)
'get_table_id:bc.DEFAULT.CFMOTO.CF_LOAD_PACKING', (1240064, 51)
'get_table_id:bc.DEFAULT.CFMOTO.V_TCER_COQ_INLAND_TWO_PRINT_UPLOAD_RECORD', (1240576, 51)
'get_table_id:bc.DEFAULT.CFMOTO.CF_MACHINING_ALUMINUM', (1241088, 51)
'get_table_id:bc.DEFAULT.CFMOTO.TCER_COQ_INLAND_TWO_PRINT_UPLOAD_RECORD', (1241600, 51)
'get_table_id:bc.DEFAULT.CFMOTO.CF_BARCODE_KEY_PART', (1242112, 51)
'get_table_id:bc.DEFAULT.CFMOTO.CF_BARCODE_INVENTORY', (1242624, 51)
'get_table_id:bc.DEFAULT.CFMOTO.SYS_USER', (1243136, 51)
'get_table_id:bc.DEFAULT.CFMOTO.TCER_COQ_INLAND_FOUR_PRINT_UPLOAD_RECORD', (1243648, 51)
'get_table_id:bc.DEFAULT.CFMOTO.CF_PORT_DELIVERY_RECORD', (1244160, 51)
'get_table_id:bc.DEFAULT.CFMOTO.CF_BARCODE_MATERIALS', (1244672, 51)
'get_table_id:bc.DEFAULT.CFMOTO.CF_REPORT_WORK_RECORD', (1245184, 51)
'get_table_id:bc.DEFAULT.CFMOTO.CF_ENGINE_MACHINE', (1245696, 51)
'get_table_id:bc.DEFAULT.CFMOTO.CF_SALE_INVOICE_HEADER', (1246208, 51)
'get_table_id:bc.DEFAULT.CFMOTO.CF_SALE_INVOICE_RECORD', (1246720, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_RDC_MATERIALPLUS_D', (1247232, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_RDC_MATERIALPLUS', (1247744, 51)
'get_table_id:bpm.BPMDB.DBO.CF_RDC_TEMPPROCESSNOTIFICATION_P', (1248256, 51)
'get_table_id:bpm.BPMDB.DBO.CF_SALE_FREIGHTALLOCATIONSTATEMENT_P', (1248768, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_FREIGHTALLOCATIONSTATEMENT_P', (1249280, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_FIN_NONPRODUCTIONPAYMENTAPPLICATION_P_D', (1249792, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_NONPRODUCTIONPAYMENTAPPLICATION_P_D', (1250304, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_MATERIALPLUS', (1250816, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SAP_FICO_FKSQ_D', (1251328, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SAP_FICO_FKSQ_D', (1251840, 51)
'get_table_id:bpm.BPMDB.DBO.CF_CW_MONTHLYFUNDDEMAND_P_D2', (1252352, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_CW_MONTHLYFUNDDEMAND_P_D2', (1252864, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SAP_FICO_FKSQ_LX_ZYD', (1253376, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SAP_FICO_FKSQ_LX_ZYD', (1253888, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_FIN_NONPRODUCTIONPAYMENTAPPLICATION_P', (1254400, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_NONPRODUCTIONPAYMENTAPPLICATION_P', (1254912, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SAP_FICO_FKSQ_LX_SUBSIDIARY', (1255424, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SAP_FICO_FKSQ_LX_SUBSIDIARY', (1255936, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_TEMPPROCESSNOTIFICATION_P', (1256448, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SAP_FICO_FKSQ_LX_WB', (1256960, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SAP_FICO_FKSQ_LX_WB', (1257472, 51)
'get_table_id:bpm.BPMDB.DBO.CF_CW_MONTHLYFUNDDEMAND_P_D1', (1257984, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_CW_MONTHLYFUNDDEMAND_P_D1', (1258496, 51)
'get_table_id:bpm.BPMDB.DBO.CF_SALE_FREIGHTALLOCATIONSTATEMENT_P_D', (1259008, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_FREIGHTALLOCATIONSTATEMENT_P_D', (1259520, 51)
'get_table_id:bpm.BPMDB.DBO.CF_CW_MONTHLYFUNDDEMAND_P', (1260032, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_CW_MONTHLYFUNDDEMAND_P', (1260544, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_BPMINSTTASKS', (1261056, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_SH_FKSQ', (1261568, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SH_FKSQ', (1262080, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SAP_FICO_FKSQ', (1262592, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SAP_FICO_FKSQ', (1263104, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_MATERIALPLUS_D', (1263616, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_SAP_BPM_ORD_SUM', (1264128, 51)
'get_table_id:bpm.CFDW.DBO.CF_SAP_ORD_SUM', (1264640, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_EAM_ASSET', (1265152, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_BARCODEINFO', (1265664, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_DMS_CDHZ', (1266176, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_ZEEHO_AFTERMARKET_BOM_2DMS', (1266688, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MATERIAL_TYPE', (1267200, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_BARCODEINFO_TMP', (1267712, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.V_MATERIAL_GROUP', (1268224, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.ODS_V_FR_PP_DEPT_REACTION', (1268736, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.ODS_FR_PP_ORDER_TYPE', (1269248, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.FRM_DIM_DATEYMD', (1269760, 51)
'get_table_id:h4p-sap.H4P.CFDBUSER.FRM_DATEYM', (1270272, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ADM_RECEPTIONACCOMMODATION', (1270784, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_RECEPTIONACCOMMODATION', (1271296, 51)
'get_table_id:bpm.BPMDB.DBO.CF_GY_JYDCORRECT_P', (1271808, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_GY_JYDCORRECT_P', (1272320, 51)
'get_table_id:mid_cfdata.DEFAULT.CFDATA.CF_APP_TBOX_CARTYPE_REL3', (1272832, 51)
'get_table_id:data-center.CFD.CFHR.DWD_SPECIAL_POST_CERTIFICATE', (1273344, 51)
'get_table_id:bpm.CFDW.DBO.DWD_SPECIAL_POST_CERTIFICATE', (1273856, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_SERVICE_PACKAGE', (1274368, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_SERVICE_PACKAGE', (1274880, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.TM_COMPANY', (1275392, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_TM_COMPANY', (1275904, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BA_USE_VHCL', (1276416, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BA_USE_VHCL', (1276928, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.DEALER_REGIONAL', (1277440, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_DEALER_REGIONAL', (1277952, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.VS_BU_CUSTOMER_ORDER_DTL', (1278464, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_VS_BU_CUSTOMER_ORDER_DTL', (1278976, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.OS_BA_COUNTRY', (1279488, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_OS_BA_COUNTRY', (1280000, 51)
'get_table_id:SRM.DEFAULT.SRM.HPFM_INV_ORGANIZATION', (1280512, 51)
'get_table_id:SRM.DEFAULT.SRM.SSRC_PRICE_LIB_LN', (1281024, 51)
'get_table_id:SRM.DEFAULT.SRM.SPFM_REL_TABLE_RECORD_SRM_CFMOTO', (1281536, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SPFM_REL_TABLE_RECORD_SRM_CFMOTO', (1282048, 51)
'get_table_id:SRM.DEFAULT.SRM.SMDM_ITEM', (1282560, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_AFTERMARKETSALEPRICE_P', (1283072, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_AFTERMARKETSALEPRICE_P', (1283584, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_PM_SCENEITEMABNORMAL_D1', (1284096, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_SALEPRICEADD_P', (1284608, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_SALEPRICEADD_P', (1285120, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_VQA_QUALITYINFORMATIONFEEDBACK', (1285632, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_SALEPRICEADJUST_P', (1286144, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_SALEPRICEADJUST_P', (1286656, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_NEWPRODUCTDEVELOPMENTAGREEMENT_P_D', (1287168, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ADM_RECEPTIONACCOMMODATION_D', (1287680, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_RECEPTIONACCOMMODATION_D', (1288192, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_FIN_STOPROCUREMENTREQUEST_P_D', (1288704, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_STOPROCUREMENTREQUEST_P_D', (1289216, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_PM_SCENEITEMABNORMAL', (1289728, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ADM_RECEPTIONACCOMMODATION_D_1', (1290240, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_RECEPTIONACCOMMODATION_D_1', (1290752, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_SALEPRICEADD_P_D', (1291264, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_SALEPRICEADD_P_D', (1291776, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_AFTERMARKETSALEPRICE_P_D', (1292288, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_AFTERMARKETSALEPRICE_P_D', (1292800, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_SALEPRICEADJUST_P_D', (1293312, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_SALEPRICEADJUST_P_D', (1293824, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_IT_ELEFILEDOWNLOAD', (1294336, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_IT_ELEFILEDOWNLOAD', (1294848, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_FIN_STOPROCUREMENTREQUEST_P', (1295360, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_STOPROCUREMENTREQUEST_P', (1295872, 51)
'get_table_id:bpm.BPMDB.DBO.CF_GY_JYDCORRECT_P_D', (1296384, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_GY_JYDCORRECT_P_D', (1296896, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.AFVV', (1297408, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_HPFM_INV_ORGANIZATION', (1297920, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SMDM_ITEM', (1298432, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_SSRC_PRICE_LIB_LN', (1298944, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_HPFM_OPERATION_UNIT', (1299456, 51)
'get_table_id:data-center.CFD.CFSRM.ODS_IAM_USER', (1299968, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.MKAL', (1300480, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_MKAL', (1300992, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_RDC_EMERGENCYTECHNICALREFORM_D', (1301504, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_EMERGENCYTECHNICALREFORM_D', (1302016, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ADM_XZZDYP', (1302528, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_XZZDYP', (1303040, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_TRIALVEHICLEMATERIALPURCHASERESTRUCTUR_P_D', (1303552, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASERESTRUCTUR_P_D', (1304064, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ADM_SERVICEPROJECTAPPROVAL', (1304576, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_SERVICEPROJECTAPPROVAL', (1305088, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_ZEEHOMATERIALSALESPRICE_P_D2', (1305600, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_ZEEHOMATERIALSALESPRICE_P_D2', (1306112, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_ADM_RECEPTIONAPPLICATION_P', (1306624, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_RECEPTIONAPPLICATION_P', (1307136, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_SIPU_DOWNCOUNT', (1307648, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_MARKETCOMPLAINTHANDLING', (1308160, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_MARKETCOMPLAINTHANDLING', (1308672, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_ZEEHOMATERIALSALESPRICE_P', (1309184, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_ZEEHOMATERIALSALESPRICE_P', (1309696, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_NEWPRODUCTDEVELOPMENTAGREEMENT_P_D', (1310208, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZL_CONFIRMATIONSWITCHINGINFORMATION_D', (1310720, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_CONFIRMATIONSWITCHINGINFORMATION_D', (1311232, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_PM_SCENEITEMABNORMAL_D1', (1311744, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_PM_SCENEITEMABNORMAL', (1312256, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_CONTRACTAPPROVALAPPLICATIONXS_P', (1312768, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_CONTRACTAPPROVALAPPLICATIONXS_P', (1313280, 51)
'get_table_id:bpm.BPMDB.DBO.CF_EAM_OUTSOURCINGMAINTENANCE', (1313792, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_EAM_OUTSOURCINGMAINTENANCE', (1314304, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_VQA_PARTSSPECSISSUEFEEDBACK', (1314816, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_PARTSSPECSISSUEFEEDBACK', (1315328, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_ZEEHOMATERIALSALESPRICE_P_D', (1315840, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_ZEEHOMATERIALSALESPRICE_P_D', (1316352, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ADM_TAXI_P', (1316864, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_TAXI_P', (1317376, 51)
'get_table_id:bpm.BPMDB.DBO.CF_SALE_MATERIALPURCHASING_P_D', (1317888, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_MATERIALPURCHASING_P_D', (1318400, 51)
'get_table_id:bpm.BPMDB.DBO.CF_ZL_FEEDBACK_P_D', (1318912, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ZL_FEEDBACK_P_D', (1319424, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ADM_ZDYPYDLFT', (1319936, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_ZDYPYDLFT', (1320448, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_RDC_RESTRUCTURINGTASK', (1320960, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_RESTRUCTURINGTASK', (1321472, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_ACCESSORIES_P', (1321984, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_ACCESSORIES_P', (1322496, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_RDC_EMERGENCYTECHNICALREFORM', (1323008, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_EMERGENCYTECHNICALREFORM', (1323520, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ADM_FINALACCEPTANCEOFINFRASTRUCTUREPROJECT', (1324032, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_FINALACCEPTANCEOFINFRASTRUCTUREPROJECT', (1324544, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_RDC_RESTRUCTURINGTASK_D', (1325056, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_RESTRUCTURINGTASK_D', (1325568, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_QUALITYINFORMATIONFEEDBACK', (1326080, 51)
'get_table_id:bpm.BPMDB.DBO.CF_SALE_MATERIALPURCHASING_P', (1326592, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_MATERIALPURCHASING_P', (1327104, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_TRIALVEHICLEMATERIALPURCHASERESTRUCTUR_P', (1327616, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_TRIALVEHICLEMATERIALPURCHASERESTRUCTUR_P', (1328128, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_ALLOWANCE', (1328640, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_ALLOWANCE', (1329152, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_ZEEHOPARTSRETURN_P', (1329664, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_ZEEHOPARTSRETURN_P', (1330176, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_ZEEHOPARTSRETURN_P_D', (1330688, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_ZEEHOPARTSRETURN_P_D', (1331200, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_SCA_IMAGESHOP_CHECKANDACCEPT_P', (1331712, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_IMAGESHOP_CHECKANDACCEPT_P', (1332224, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_ADM_SETTLEMENTINFRASTRUCTUREPROJECTS', (1332736, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_ADM_SETTLEMENTINFRASTRUCTUREPROJECTS', (1333248, 51)
'get_table_id:bpm.BPMDB.DBO.CF_FIN_DISPOSE_P', (1333760, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_FIN_DISPOSE_P', (1334272, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SUP_ALLOWANCE_D1', (1334784, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SUP_ALLOWANCE_D1', (1335296, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_SCA_IMAGESHOP_CHECKANDACCEPT_GJ_P', (1335808, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_IMAGESHOP_CHECKANDACCEPT_GJ_P', (1336320, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_ZEEHOPARTSRETURN_P_D3', (1336832, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_ZEEHOPARTSRETURN_P_D3', (1337344, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_ZEEHOPARTSRETURN_P_D2', (1337856, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_ZEEHOPARTSRETURN_P_D2', (1338368, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_RDC_SIPU_DOWNCOUNT', (1338880, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_VQA_PARTOTSRECOGNITION', (1339392, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_VQA_PARTOTSRECOGNITION', (1339904, 51)
'get_table_id:bpm.BPMDB.DBO.YZOA_FM_HDR', (1340416, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_YZOA_FM_HDR', (1340928, 51)
'get_table_id:bpm.CFBPMDB.DBO.CF_SCA_IMAGESHOP_CHECKANDACCEPT_P_CONTACT', (1341440, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SCA_IMAGESHOP_CHECKANDACCEPT_P_CONTACT', (1341952, 51)
'get_table_id:data-center.CFD.CFHR.V_PS_CF_EMP_DORM_INF', (1342464, 51)
'get_table_id:bpm.CFDW.DBO.PS_CF_EMP_DORM_INF', (1342976, 51)
'get_table_id:bpm.CFDW.DBO.CF_MDM_DEALER_STORE', (1343488, 51)
'get_table_id:bpm.CFDW.DBO.DIM_SAP_H_MATERIAL_DELTA', (1344000, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZSDX1', (1344512, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZSDX1', (1345024, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZSDX2', (1345536, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZSDX2', (1346048, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.EINA', (1346560, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_EINA', (1347072, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTSD002', (1347584, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTSD002', (1348096, 51)
'get_table_id:h4p-sap.H4P.SAPHANADB.ZTFI004_ADD', (1348608, 51)
'get_table_id:data-center.CFD.CFSAP.ODS_ZTFI004_ADD', (1349120, 51)
'get_table_id:cfdms.DEFAULT.CFDMS.SE_BU_CLAIM_WORK', (1349632, 51)
'get_table_id:data-center.CFD.CFDMS.ODS_SE_BU_CLAIM_WORK', (1350144, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_MARKETCOMPLAINTHANDLING_D2', (1350656, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_MARKETCOMPLAINTHANDLING_D2', (1351168, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_RDC_DEVELOPEDSPAREPARTSSCRAP', (1351680, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_DEVELOPEDSPAREPARTSSCRAP', (1352192, 51)
'get_table_id:bpm.BPMDB.DBO.CF_IT_BIGDATAPLATFORM', (1352704, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_IT_BIGDATAPLATFORM', (1353216, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_MARKETCOMPLAINTHANDLING_D1', (1353728, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_MARKETCOMPLAINTHANDLING_D1', (1354240, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_ZEEHOCUSTOMERCLAIM', (1354752, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_ZEEHOCUSTOMERCLAIM', (1355264, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_ZEEHOCUSTOMERCLAIM_D', (1355776, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_ZEEHOCUSTOMERCLAIM_D', (1356288, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_RDC_DEVELOPEDSPAREPARTSSCRAP_D', (1356800, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_RDC_DEVELOPEDSPAREPARTSSCRAP_D', (1357312, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_MARKETCOMPLAINTHANDLING_D4', (1357824, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_MARKETCOMPLAINTHANDLING_D4', (1358336, 51)
'get_table_id:bpm.CFBPMNEW.DBO.CF_SALE_MARKETCOMPLAINTHANDLING_D3', (1358848, 51)
'get_table_id:data-center.CFD.CFBPM.ODS_CF_SALE_MARKETCOMPLAINTHANDLING_D3', (1359360, 51)
