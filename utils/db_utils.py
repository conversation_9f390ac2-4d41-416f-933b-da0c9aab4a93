# -*- coding: utf-8 -*-
import os
import shelve

import psycopg2
import pymssql
from urllib.parse import quote_plus as url_quote

from odps import ODPS
from odps.readers import CsvRecordReader
from sqlalchemy import create_engine
from cachetools import TTL<PERSON>ache, cached
import oracledb
from config import get_config
ttl_cache = TTLCache(maxsize=100, ttl=36000)
db_config = get_config()

# @cached(ttl_cache)
def get_pg_query(sql, config: dict = None,params: list = None):
    if not config:
        config = db_config['db.hufu']
    conn = psycopg2.connect(
        database=config['dbName'],
        user=config['userName'],
        password=config['password'],
        host=config['server'],
        port=config['port']
    )
    cursor = conn.cursor()
    if params:
        cursor.execute(sql, params)
    else:
        cursor.execute(sql)

    res = None
    if cursor.statusmessage.startswith('INSERT'):
        conn.commit()
    else:
        res = cursor.fetchall()
    cursor.close()
    conn.close()
    return res

def get_metadata_query(sql):
    _config = db_config['db.openmeta']
    return get_pg_query(sql, config=_config)

def get_mysql_query(sql, _config: dict = None):
    if not _config:
        _config = db_config['db.openmeta']
    _db_url = f"mysql+pymysql://{_config['username']}:{url_quote(_config['password'])}@{_config['server']}:{_config['port']}/{_config['dbName']}"
    _engine = create_engine(_db_url,
                            max_overflow=0,
                            pool_size=5,
                            pool_timeout=30,
                            pool_recycle=-1
                            )
    conn = _engine.connect()
    cursor = conn.exec_driver_sql(sql)
    res = cursor.fetchall()
    cursor.close()
    conn.close()
    return res

def get_mc_query(sql, _config: dict = None):
    o = ODPS(
        # （推荐）确保已设置环境变量。
        # 确保ALIBABA_CLOUD_ACCESS_KEY_ID环境变量设置为用户 Access Key ID。
        access_id=os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),
        # 确保ALIBABA_CLOUD_ACCESS_KEY_SECRET环境变量设置为用户Access Key Secret。
        secret_access_key=os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),

        project='cfdata',
        endpoint='https://service.cn-hangzhou.maxcompute.aliyun.com/api',
    )
    res = o.execute_sql_interactive(sql, use_mcqa_v2=True,quota_name = 'MaxQA1',fallback='all',hints={'odps.namespace.schema':'TRUE',"odps.sql.submit.mode" : "script"})
    res1 = []
    with res.open_reader() as reader:
        if  not isinstance(reader,CsvRecordReader):
            if reader.count > 0:
                for record in reader:
                    res1.append(record.values)
    return res1

def get_mssql_query(sql, _config: dict = None):
    if not _config:
        _config = db_config['db.bpm']
    conn = pymssql.connect(
        database=_config['dbName'],
        user=_config['userName'],
        password=_config['password'],
        host=_config['server'],
        port=_config['port']
    )
    # db_str = 'mssql+pymssql://sa:CfdL@0605@***********:1433/bpmdb'
    # _engine = create_engine(db_str,deprecate_large_types=True)
    # conn = _engine.connect()
    cursor = conn.cursor()
    cursor.execute(sql)
    res = cursor.fetchall()
    cursor.close()
    conn.close()
    return res


def get_hana_query(sql,params: tuple = None, _config: dict = None):
    if not _config:
        _config = db_config['db.cfd']
    if _config['server'] == '************':
        _config['server'] = '*************'
    engine = create_engine(f'hana://{_config['username']}:{url_quote(_config['password'])}@{_config['server']}:{_config['port']}')
    conn = engine.connect()
    if params:
        cursor = conn.exec_driver_sql(sql,params)
    else:
        cursor = conn.exec_driver_sql(sql)
    res = None
    if cursor.returns_rows:
        res = cursor.fetchall()
    else:
        conn.commit()
    cursor.close()
    conn.close()
    return res

def get_oracle_query(sql, _config: dict = None):
    if not _config:
        _config = db_config['db.ps']
    engine = create_engine(f'oracle+oracledb://{_config['username']}:{url_quote(_config['password'])}@{_config['server']}:{_config['port']}/{_config['dbName']}')
    conn = engine.connect()
    cursor = conn.exec_driver_sql(sql)
    res = cursor.fetchall()
    cursor.close()
    conn.close()
    return res

def buffer_cache_put(key, value):
    with shelve.open('cache_db') as shelf:
        shelf[key] = value


def buffer_cache_get(key):
    with shelve.open('cache_db') as shelf:
        if key in shelf:
            return shelf[key]
        else:
            return None


def buffer_cache_del(key):
    with shelve.open('cache_db') as shelf:
        if key in shelf:
            del shelf[key]


if __name__ == '__main__':
    query = '''
    set odps.namespace.schema=TRUE ;
    drop table cfdata.cfsap.ods_t001;
    drop table cfdata.cfsap.ods_t005t;
    drop table cfdata.cfsap.ods_t006a;
    drop table cfdata.cfsap.ods_tcurt;
    drop table cfdata.cfsap.ods_zmmt0021;
    drop table cfdata.cfsap.ods_zsdx4;
    drop table cfdata.cfsap.v_barcodeinfo;
    drop table cfdata.cfsap.v_material;
    '''
    # res = get_pg_query(query)
    # for re in res:
    #     dict_s = json.loads(re[11])
    #     print(dict_s)
    # buffer_cache_put('test:key1','abc')
    # buffer_cache_del('test:key1')
    res = get_mc_query(query)
    print(res)
