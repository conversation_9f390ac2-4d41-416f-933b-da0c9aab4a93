import re
from typing import Set, List

from sqllineage.runner import LineageRunner
from sqllineage.core.metadata.sqlalchemy import SQLAlchemyMetaDataProvider
from utils import get_logger

logger = get_logger(__name__)

def extract_cfdms_tables_from_sql(sql: str, dialect: str = 'ansi', metadata_provider = None) -> Set[str]:
    """
    Extract all referenced tables from SQL and collect tables with "cfdms" schema into a set.
    
    Args:
        sql (str): The SQL query to parse
        dialect (str, optional): SQL dialect to use for parsing. Defaults to 'ansi'.
        metadata_provider (optional): SQLAlchemy metadata provider for database connection. Defaults to None.
    
    Returns:
        Set[str]: A set of table names with "cfdms" schema
    """
    # Clean up the SQL query
    pattern1 = r'\$\{(.+)\}'
    pattern2 = r'top(\s)+\d+'
    cleaned_sql = re.sub(pattern1, '', sql)
    cleaned_sql = re.sub(pattern2, '', cleaned_sql)
    
    try:
        # Parse the SQL query to extract tables
        if metadata_provider:
            lineage = LineageRunner(cleaned_sql, metadata_provider=metadata_provider)
        else:
            lineage = LineageRunner(cleaned_sql, dialect=dialect)
        
        # Get all source tables
        all_tables = set(str(table) for table in lineage.source_tables)
        
        # Filter tables to only include those with "cfdms" schema
        cfdms_tables = {table for table in all_tables if table.lower().startswith('cfdms.')}
        
        return cfdms_tables
    except Exception as e:
        logger.error(f"Error extracting tables from SQL: {e}")
        return set()

def extract_all_tables_from_sql(sql: str, dialect: str = 'ansi', metadata_provider = None) -> List[str]:
    """
    Extract all referenced tables from SQL.
    
    Args:
        sql (str): The SQL query to parse
        dialect (str, optional): SQL dialect to use for parsing. Defaults to 'ansi'.
        metadata_provider (optional): SQLAlchemy metadata provider for database connection. Defaults to None.
    
    Returns:
        List[str]: A list of all table names referenced in the SQL
    """
    # Clean up the SQL query
    pattern1 = r'\$\{(.+)\}'
    pattern2 = r'top(\s)+\d+'
    cleaned_sql = re.sub(pattern1, '', sql)
    cleaned_sql = re.sub(pattern2, '', cleaned_sql)
    
    try:
        # Parse the SQL query to extract tables
        if metadata_provider:
            lineage = LineageRunner(cleaned_sql, metadata_provider=metadata_provider)
        else:
            lineage = LineageRunner(cleaned_sql, dialect=dialect)
        
        # Get all source tables
        all_tables = [str(table) for table in lineage.source_tables]
        
        return all_tables
    except Exception as e:
        logger.error(f"Error extracting tables from SQL: {e}")
        return []

def extract_tables_by_schema(sql: str, schema: str, dialect: str = 'ansi', metadata_provider = None) -> Set[str]:
    """
    Extract all referenced tables from SQL and collect tables with the specified schema into a set.
    
    Args:
        sql (str): The SQL query to parse
        schema (str): The schema name to filter by
        dialect (str, optional): SQL dialect to use for parsing. Defaults to 'ansi'.
        metadata_provider (optional): SQLAlchemy metadata provider for database connection. Defaults to None.
    
    Returns:
        Set[str]: A set of table names with the specified schema
    """
    # Clean up the SQL query
    pattern1 = r'\$\{(.+)\}'
    pattern2 = r'top(\s)+\d+'
    cleaned_sql = re.sub(pattern1, '', sql)
    cleaned_sql = re.sub(pattern2, '', cleaned_sql)
    
    try:
        # Parse the SQL query to extract tables
        if metadata_provider:
            lineage = LineageRunner(cleaned_sql, metadata_provider=metadata_provider)
        else:
            lineage = LineageRunner(cleaned_sql, dialect=dialect)
        
        # Get all source tables
        all_tables = set(str(table) for table in lineage.source_tables)
        
        # Filter tables to only include those with the specified schema
        schema_tables = {table for table in all_tables if table.lower().startswith(f"{schema.lower()}.")}
        
        return schema_tables
    except Exception as e:
        logger.error(f"Error extracting tables from SQL: {e}")
        return set()

if __name__ == "__main__":
    # Example usage
    test_sql = """
    SELECT a.column1, b.column2
    FROM cfdms.table1 a
    JOIN other_schema.table2 b ON a.id = b.id
    JOIN cfdms.table3 c ON a.id = c.id
    WHERE a.column1 = 'value'
    """
    
    cfdms_tables = extract_cfdms_tables_from_sql(test_sql)
    print(f"CFDMS Tables: {cfdms_tables}")
    
    all_tables = extract_all_tables_from_sql(test_sql)
    print(f"All Tables: {all_tables}")
    
    custom_schema_tables = extract_tables_by_schema(test_sql, "other_schema")
    print(f"Custom Schema Tables: {custom_schema_tables}")
