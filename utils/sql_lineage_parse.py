import logging

from sqllineage.runner import LineageRunner
from sqllineage.config import SQLLineageConfig
from sqllineage.core.metadata.sqlalchemy import SQLAlchemyMetaDataProvider
from utils import get_logger
from utils import HAHA_URL
from utils.db_utils import get_hana_query

logger = get_logger(__name__,logging.DEBUG)

def view_lineage_parser(view_name,db_type):
    pass


if __name__ == "__main__":
    provider = SQLAlchemyMetaDataProvider(url=HAHA_URL)
    query = '''
select
  distinct case when fname is null then '未维护' when fname ='' then '未维护' else trim(fname) end as fname 
from cffr.ODS_FR_SALES_BASE_CARTYPEINFO
where fcategory='两轮车' and fseries='民用' -- 徐一凡2024-07-08要求限定
and 1=1 
and 1=1 
order by fname   
'''
    query = query.replace('"','')
    with SQLLineageConfig(DEFAULT_SCHEMA = 'CFQISUSER') as config:
        logger.debug(LineageRunner.supported_dialects())
        lineage = LineageRunner(query,metadata_provider=provider)
        for table in lineage.source_tables:
            query = f'select count(*) from {table}'
            res = get_hana_query(query)
            if not res:
                print(table)
            else:
                print(f'table: {table} valid!')

