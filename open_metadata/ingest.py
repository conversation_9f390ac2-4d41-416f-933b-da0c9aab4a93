# -*- coding: utf-8 -*-

import json
import sys
from dataclasses import dataclass

import requests

from config import get_config
from open_metadata import (
    LINEAGE_URL,
    DASHBOAD_DATAMODEL_URL,
    HEADERS,
    DataModel,
    Dashboard,
    DASHBOAD_URL,
    Metadata_client,
)
from utils import get_logger
from metadata.generated.schema.entity.services.databaseService import DatabaseService
from metadata.generated.schema.api.services.createDatabaseService import (
    CreateDatabaseServiceRequest,
)
from metadata.generated.schema.entity.services.databaseService import (
    DatabaseService,
    DatabaseServiceType,
    DatabaseConnection,
)
from metadata.generated.schema.entity.services.connections.database.common.basicAuth import (
    BasicAuth,
)
from metadata.generated.schema.entity.services.connections.database.mysqlConnection import (
    MysqlConnection,
)
from metadata.generated.schema.entity.data.table import Table, Column
from metadata.generated.schema.type.entityReference import EntityReference
from metadata.generated.schema.entity.data.table import TableType
import metadata.ingestion.source.database.common_db_source
from metadata.utils import fqn
from metadata.utils.logger import ingestion_logger

from utils.db_utils import get_hana_query

logger = get_logger(__name__)


def add_lineage(sid, stype, tid, ttype, desc=None):
    data = dict(edge={
        'fromEntity': {
            'id': sid,
            'type': stype
        },
        'toEntity': {
            'id': tid,
            'type': ttype
        },
        'lineageDetails': {
            'description': desc,
            'source': 'ExternalTableLineage'
        }
    })
    try:
        resp = requests.put(LINEAGE_URL, json=data, headers=HEADERS)
        resp.raise_for_status()  # Raises an HTTPError for bad responses (4xx and 5xx)
    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error occurred: {http_err}")
    except requests.exceptions.RequestException as req_err:
        logger.error(f"Request error occurred: {req_err}")


def add_lineage_by_query(service, query):

    database_service: DatabaseService = Metadata_client.get_by_name(
        entity=DatabaseService, fqn=service
    )

    Metadata_client.add_lineage_by_query(
        database_service=database_service,
        check_patch=False,
        timeout=300, # timeout in seconds
        sql=query # your sql query
    )


def create_dataservice(**kwargs):
    create_service = CreateDatabaseServiceRequest(
        name="test-service-table",
        serviceType=DatabaseServiceType.Mysql,
        connection=DatabaseConnection(
            config=MysqlConnection(
                username=kwargs['username'],
                authType=BasicAuth(password=kwargs['password']),
                hostPort=f'{kwargs["host"]}:{kwargs["port"]}',
            )
        ),
    )
    service_entity = metadata.create_or_update(data=create_service)
    return service_entity


def get_DatabaseService_by_name(name):
    service = metadata.get_by_name(entity=DatabaseService, fqn=name)
    return service


# 添加dashboad datamodel
def dashboad_datamodel_add(DataModel: DataModel):
    dashboad_data = {
        'columns': [],
        'dataModelType': DataModel.dataModelType,
        'name': DataModel.name,
        'service': DataModel.service,
        'displayName': DataModel.displayName,
        'description': DataModel.description,
        'sql': DataModel.sql
    }
    dashboad_json = json.dumps(dashboad_data)
    resp = requests.put(DASHBOAD_DATAMODEL_URL, dashboad_json, headers=HEADERS)
    if resp.status_code in (200, 201):
        id = resp.json()['id']
        return id
    else:
        logger.error(f'datamodel {DataModel.name} put failed: {resp.status_code}')
        return None


def dashboad_add(dashboard: Dashboard) -> str:
    try:
        dashboard_data = {
            "name": dashboard.name,
            "service": dashboard.service,
            "displayName": dashboard.displayName,
            "description": dashboard.description,
            "sourceUrl": dashboard.sourceUrl,
            "dataModels": dashboard.dataModels
        }

        resp = requests.put(DASHBOARD_URL, json=dashboard_data, headers=HEADERS)

        if resp.status_code in (200, 201):
            response_json = resp.json()
            if 'id' in response_json:
                return response_json['id']
            else:
                logger.error(f"Response missing 'id' field for dashboard {dashboard.name}")
                return None
        else:
            logger.error(f"Dashboard {dashboard.name} PUT failed with status code {resp.status_code}: {resp.text}")
            return None
    except requests.RequestException as e:
        logger.error(f"Request failed for dashboard {dashboard.name}: {e}")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Failed to decode JSON response for dashboard {dashboard.name}: {e}")
        return None


def fetch_table_metadata(cursor, schema_name):
    cursor.execute(f"""
        SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, IS_NULLABLE
        FROM TABLE_COLUMNS
        WHERE SCHEMA_NAME = '{schema_name}'
    """)
    tables = {}
    for row in cursor.fetchall():
        table_name = row[0]
        if table_name not in tables:
            tables[table_name] = {'columns': []}
        column = Column(
            name=row[1],
            dataType=row[2],
            dataLength=row[3],
            defaultValue=row[4],
            nullable=row[5] == 'YES'
        )
        tables[table_name]['columns'].append(column)
    return tables


def fetch_view_metadata(cursor, schema_name):
    cursor.execute(f"""
        SELECT VIEW_NAME, DEFINITION
        FROM VIEWS
        WHERE SCHEMA_NAME = '{schema_name}'
    """)
    views = {}
    for row in cursor.fetchall():
        view_name = row[0]
        definition = row[1]
        views[view_name] = definition
    return views


def update_metadata_to_openmetadata(connection, schema_name):
    cursor = connection.cursor()
    tables = fetch_table_metadata(cursor, schema_name)
    views = fetch_view_metadata(cursor, schema_name)

    for table_name, table_details in tables.items():
        table_fqn = fqn.build(
            metadata.get_database_service_name(),
            schema_name,
            table_name
        )
        table_entity = Table(
            id=metadata.get_table_id(table_fqn),
            name=table_name,
            description=None,
            tableType=TableType.Regular.value,
            columns=table_details['columns'],
            database=EntityReference(id=metadata.get_database_id(), type='database'),
            databaseSchema=EntityReference(id=metadata.get_database_schema_id(schema_name), type='databaseSchema')
        )
        metadata.create_or_update(table_entity)
        logger.info(f"Updated table metadata: {table_fqn}")

    for view_name, view_definition in views.items():
        view_fqn = fqn.build(
            metadata.get_database_service_name(),
            schema_name,
            view_name
        )
        view_entity = Table(
            id=metadata.get_table_id(view_fqn),
            name=view_name,
            description=None,
            tableType=TableType.View.value,
            columns=[],  # Columns can be extracted from view_definition if needed
            database=EntityReference(id=metadata.get_database_id(), type='database'),
            databaseSchema=EntityReference(id=metadata.get_database_schema_id(schema_name), type='databaseSchema')
        )
        metadata.create_or_update(view_entity)
        logger.info(f"Updated view metadata: {view_fqn}")

        # Extract dependencies from view definition and add lineage
        dependencies = extract_dependencies_from_view(view_definition)
        for dependency in dependencies:
            add_lineage(
                sid=metadata.get_table_id(fqn.build(metadata.get_database_service_name(), schema_name, dependency)),
                stype='table',
                tid=metadata.get_table_id(view_fqn),
                ttype='table',
                desc=f'{dependency} => {view_name}'
            )
            logger.info(f"Added lineage from {dependency} to {view_name}")


def extract_dependencies_from_view(view_definition):
    # This is a simplified example. In practice, you may need a more robust SQL parser.
    dependencies = []
    for line in view_definition.split('\n'):
        line = line.strip().lower()
        if line.startswith('from') or line.startswith('join'):
            parts = line.split()
            for part in parts:
                if '.' in part and part != 'as':
                    schema, table = part.split('.')
                    dependencies.append(table.replace(',', ''))
    return dependencies


if __name__ == '__main__':
    config = get_config()
    h4p_config = config['db.h4p']
    # connection = get_hana_query(h4p_config)
    schema_name = 'CFDBUSER'  # Update this with your schema name
    sql = f'''
insert into cfdata.dws_qm_vehoutinfo_fact
with a as (--车辆出库汇总
	select a.fareaid, a.fcountry, a.ffactory, a.fcategory, a.fseries, a.fcartype, a.fvolume, a.fproductiondate, a.foutstockdate,
		count(a.fcjh) ftotal
	from cfdata.dwd_qm_vehlastout_info_fact as a
	where ifnull(a.fproductiondate,'') != ''
	  and ifnull(a.foutstockdate,'') != ''
	group by a.fareaid, a.fcountry, a.ffactory, a.fcategory, a.fseries, a.fcartype, a.fvolume, a.fproductiondate, a.foutstockdate
)
--车辆出库汇总-倒推月份
select v.fareaid, v.fcountry, v.ffactory, ifnull(v.fcategory,'空') fcategory, ifnull(v.fseries,'空') fseries,
    v.fcartype, v.fvolume, d.fbasedate, d.fdtmonth,
    sum(v.ftotal) ftotal, current_timestamp insert_time
from cfdata.dim_basedate_2 as d,a as v
where v.fproductiondate < d.fbasedate
    and v.fproductiondate >= d.fstartdate
    and v.foutstockdate < d.fbasedate
    and v.foutstockdate >= d.fstartdate
group by v.fareaid, v.fcountry, v.ffactory,v.fcategory,v.fseries,v.fcartype,v.fvolume,d.fbasedate,d.fdtmonth
;
    '''
    add_lineage_by_query('data-center',sql)
    # update_metadata_to_openmetadata(connection, schema_name)