import string
from dataclasses import dataclass, field, KW_ONLY
from urllib.parse import urljoin
METADATA_API_HOST = 'http://************:8585'
API_URL = urljoin(METADATA_API_HOST, 'api/v1/')
AUTHORIZATION = 'Bearer eyJraWQiOiJHYjM4OWEtOWY3Ni1nZGpzLWE5MmotMDI0MmJrOTQzNTYiLCJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************.AwL3eGxLFnDsUG3795Yr8Vug-K_TG08BMK4SFnRrTASq1GiF2zY_5zk-IQseI0ri_ZgBqFZywWXENgAKD40p178O_WuCKVrEQIHFEkEvj5J2qzB98PPk-IU5yOAunLL8-oa3tPK-EXfppxKSGQI8rWQLeo8o_w3FP3eUyDYKauoR_8jqCqiAdz2pWEUN6OVSl9xiKgr8896VjTansUyzaQa8PnZp6-Jn7MK-sN63CJx1D60JrHBIm1P_gT_hWxv8Men9Bbuf4dV0ZDg1Pg3XcZz_S3AnrX6c3MzWoRTDiSx6X6GCYcumM2xLK7DAiIbnqWYPaWYIbjUdleC10e-BbQ'
API_KEY = 'Bearer eyJraWQiOiJHYjM4OWEtOWY3Ni1nZGpzLWE5MmotMDI0MmJrOTQzNTYiLCJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************.AwL3eGxLFnDsUG3795Yr8Vug-K_TG08BMK4SFnRrTASq1GiF2zY_5zk-IQseI0ri_ZgBqFZywWXENgAKD40p178O_WuCKVrEQIHFEkEvj5J2qzB98PPk-IU5yOAunLL8-oa3tPK-EXfppxKSGQI8rWQLeo8o_w3FP3eUyDYKauoR_8jqCqiAdz2pWEUN6OVSl9xiKgr8896VjTansUyzaQa8PnZp6-Jn7MK-sN63CJx1D60JrHBIm1P_gT_hWxv8Men9Bbuf4dV0ZDg1Pg3XcZz_S3AnrX6c3MzWoRTDiSx6X6GCYcumM2xLK7DAiIbnqWYPaWYIbjUdleC10e-BbQ'
HEADERS = {'content-type': 'application/json',
           'authorization': API_KEY}
LINEAGE_URL = urljoin(API_URL, 'lineage')
DASHBOAD_DATAMODEL_URL = urljoin(API_URL, 'dashboard/datamodels')
DASHBOAD_URL = urljoin(API_URL, 'dashboards')

from metadata.ingestion.ometa.ometa_api import OpenMetadata
from metadata.generated.schema.entity.services.connections.metadata.openMetadataConnection import (
    OpenMetadataConnection, AuthProvider,
)
from metadata.generated.schema.security.client.openMetadataJWTClientConfig import OpenMetadataJWTClientConfig

server_config = OpenMetadataConnection(
    hostPort="http://************:8585/api",
    authProvider=AuthProvider.openmetadata,
    securityConfig=OpenMetadataJWTClientConfig(
        jwtToken=API_KEY.replace('Bearer ',''),
    ),
)
Metadata_client = OpenMetadata(server_config)

@dataclass(frozen=True)
class DataModel(object):
    name: str
    service :str
    dataModelType : str
    columns : str
    _: KW_ONLY
    description: str
    displayName: str
    sql: str



@dataclass(frozen=True)
class Dashboard(object):
    name: str
    service: str
    dashboardType :str
    _: KW_ONLY
    dataModels: []
    description: str
    displayName: str
    sourceUrl: str
