import json

from cachetools import TTLCache

from config import get_config
from utils.db_utils import get_mssql_query, get_pg_query, get_hana_query, get_mysql_query, get_oracle_query
from xuelangos import logger

db_config = get_config()['db.hufu']


def get_dsconfig_by_id(id) -> dict:
    ds_query = f'''
    select 
    id,
    ds_type,
    rdbms_config_info ,
    ds_name 
    from hufu.tb_datasource
    where id = {id}
    '''
    res = get_pg_query(ds_query)

    if res:
        for id, ds_type, db_info, ds_name in res:
            rs = json.loads(db_info)
            rs['id'] = id
            rs['ds_type'] = ds_type
            rs['ds_name'] = ds_name
            rs['dbType'] = ds_type
    return rs

def get_dsconfig_by_server(server,db=None) -> dict:

    ds_query = f'''
    select 
    id,
    ds_type,
    rdbms_config_info ,
    ds_name 
    from hufu.tb_datasource
    where json(rdbms_config_info) ->> 'server' = '{server}'
    
    '''
    if db:
        ds_query = f'''{ds_query} and json(rdbms_config_info) ->> 'dbName'  = '{db}' '''
    if db == 'dms_zeeho':
        ds_query = f'''  select 
    id,
    ds_type,
    rdbms_config_info ,
    ds_name 
    from hufu.tb_datasource
    where json(rdbms_config_info) ->> 'dbName' = '{db}'
    '''
    res = get_pg_query(ds_query)
    if res:
        rs = json.loads(res[0][2])
        rs['id'] = res[0][0]
        rs['dbType'] = res[0][1]
        rs['ds_name'] = res[0][3]
    return rs

def get_taskflow_id(dbname) -> int:
    '''
    根据源表表名称查找最合适的工作流
    默认所有自动同步表为非主数据
    在业务目录下面查找同db/schema现有表最多的工作流
    :return: 合适的工作流id
    '''
    taskflow_query = f'''
    select
	tf.id,
	tf.name,
	count(*) as tcnt
from
	hufu.tb_datadev_offline_sync s
inner join hufu.tb_datasource ds on
	s.source_ds_id = ds.id
	and  
lower(case when ds_type in ('MySQL', 'SQLServer', 'PostgreSQL') then lower(json(rdbms_config_info) ->> 'dbName')
	 when ds_type = 'Sap' then 'H4P' 
	 else ds.ds_name end)
= case when ds_type = 'Sap' then 'H4P' else lower('{dbname}') end
inner join HUFU.tb_datadev_sub_node sb on
	s.node_id = sb.node_id
inner join hufu.tb_datadev_menunode tf on
	sb.task_flow_id = tf.id
	and tf.row_state = 1
	and tf.is_locked = 0
inner join HUFU.tb_datadev_menunode tk on
	sb.node_id = tk.id
	and tf.row_state = 1
	and tk.node_type = 20
group by
	tf.id,
	tf.name
order by tcnt desc 
    '''
    _res = get_pg_query(taskflow_query)
    for id, name, cnt in _res:
        if name.startswith('PRO_D'):
            logger.info(f'id:{id} name:{name} cnt:{cnt}')
            return id


def create_task_node(id, source_table, target_table) -> int:
    if source_table.count('.') == 2:
        db, schema, table_raw_name = source_table.split('.')
        source_raw_table = f'{schema}.{table_raw_name}'
    else:
        db,table_raw_name = source_table.split('.')
        schema = db
        source_raw_table = f'{schema}.{table_raw_name}'
    target_ds_id, target_ds_type = 39, 'Sap'
    if db.upper() in ('CFDBUSER','SAPHANADB'):
        source_ds_id, source_ds_type, source_ds_schema = 37, 'Sap',db.upper()
        db = 'H4P' # sap 为固定db
        source_raw_table = source_raw_table.upper()

    else:
        ds_query = f'''
        select 
        id,
        ds_type,
        json(rdbms_config_info) ->> 'schema' as schema_name 
        from hufu.tb_datasource
        where  upper(json(rdbms_config_info) ->> 'dbName') = upper('{db}')
        and status = 1
        and ds_name !~ 'test'
        and row_state = 1 
        '''
        ds_info = get_pg_query(ds_query)
        if ds_info:
            source_ds_id, source_ds_type, source_ds_schema = ds_info[0]


    if source_ds_type == 'MySQL':
        source_raw_table = table_raw_name.lower()
    node_name = f'{db}_HANA_{table_raw_name}'.upper()
    node_pid_query = f'''
    select id from hufu.tb_datadev_menunode
    where pid = {id}
    and name = '数据集成'
    '''
    res = get_pg_query(node_pid_query)
    node_pid = int(res[0][0])
    sort_query = f'''
   select max(sort) 
   from hufu.tb_datadev_menunode
    where pid = {node_pid}
    and row_state =1 
'''
    sort = int(get_pg_query(sort_query)[0][0]) + 1

    corrd_dtl_query = f'''
        with leaf_node_list as (
    select t.* 
     from HUFU.tb_datadev_sub_node_dependency  t  -- 维护上下文
     left join HUFU.tb_datadev_sub_node_dependency p on t.task_flow_id = p.task_flow_id and t.down_node_id = p.up_node_id and p.row_state =1 
    where t.task_flow_id = {id}
    and p.id is null
    and t.row_state =1 
    ),new_node as (
    select 
    down_node_id,
    coord_x,
    cast(n.coord_y as bigint)  + 80 as  coord_y,
    row_number() over(order by  cast(n.coord_y as bigint),cast(n.coord_x as bigint)) as rn
    from leaf_node_list l
    inner join hufu.tb_datadev_sub_node n on  l.down_node_id = n.node_id 
    )
    select
    down_node_id,
    coord_x,
    coord_y
    from new_node
    where rn =1 
    '''

    corrd_dtl = get_pg_query(corrd_dtl_query)[0]
    up_node_id, corrd_x, corrd_y = corrd_dtl
    col_res = get_field_mapping(source_ds_id, schema, table_raw_name)
    col_values = []
    for ord, source_column, source_type, target_column, target_data_type in col_res:
        col_values.append(
            f'''(currval('hufu.tb_datadev_menunode_id_seq'),'{source_column}','{source_type}',0,'{target_column}','{target_data_type}',0,current_timestamp,current_timestamp,28,28)''')

    insert_query = f'''
    insert into hufu.tb_datadev_menunode(id,name,pid,project_id,sort,node_type,create_time,update_time,creator_id,updater_id,row_state)
    values (nextval('hufu.tb_datadev_menunode_id_seq'),'{node_name}',{node_pid},2,{sort},20,current_timestamp,current_timestamp,28,28,1);
    
    -- 画布插入
    insert into hufu.tb_datadev_sub_node(task_flow_id,node_id,row_state,coord_x,coord_y,create_time,update_time,project_id,creator_id,updater_id)
    values ({id},currval('hufu.tb_datadev_menunode_id_seq'),1,{corrd_x},{corrd_y},current_timestamp,current_timestamp,2,28,28);
    
    -- 关系插入
  insert into hufu.tb_datadev_sub_node_dependency(task_flow_id,up_node_id,down_node_id ,status,row_state,create_time,update_time,creator_id,updater_id)
  values ({id},{up_node_id},currval('hufu.tb_datadev_menunode_id_seq'),2,1,current_timestamp,current_timestamp,28,28);
  
    -- 表同步
    
    insert into hufu.tb_datadev_offline_sync(source_ds_type,source_ds_id,target_ds_type,target_ds_id,is_parition,data_replace_rule_id,is_truncate,error_num_limit,is_inc,source_table,target_table,node_id,create_time,update_time,creator_id,updater_id,fetch_size,batch_size,channel,split_key)
    values ('{source_ds_type}',{source_ds_id},'Sap',39,0,5,1,0,0,'{source_raw_table}','{target_table}',currval('hufu.tb_datadev_menunode_id_seq'),current_timestamp,current_timestamp,28,28,500,2000,5,null);
    
    -- 字段同步
     insert into hufu.tb_datadev_sync_field_mapping (node_id,source_field,source_field_type,is_source_field_pk,target_field,target_field_type,is_target_field_pk,create_time,update_time,creator_id,updater_id)
    values {'\n,'.join(col_values)}
     ;
     commit;
     
    select '{node_name}';
    '''
    # logger.info(insert_query)
    res = get_pg_query(insert_query)[0][0]
    # logger.info(f'id:{node_pid} table:{table} node_name:{node_name} sort:{sort}')
    return res


def get_field_mapping(source_ds_id, schema, table_raw_name):
    db_dict = get_dsconfig_by_id(source_ds_id)
    source_ds_type = db_dict['ds_type']
    config = db_dict
    if source_ds_id == 125:
        db_dict['server'] = 'rm-bp17mrd652my6154rpo.mysql.rds.aliyuncs.com'
    match source_ds_type:
        case 'Sap':
            if not db_dict['server']:
                config = get_config()['db.h4p']
            # schema, table_raw_name = table_name.upper().split('.')
            table_query = f'''
            SELECT object_type, object_oid
            FROM objects
            WHERE schema_name = '{schema}'
              AND OBJECT_NAME = '{table_raw_name}'
            
            '''
            table_info = get_hana_query(table_query, _config=config)
            if table_info:
                table_type, table_oid = table_info[0]
            else:
                logger.error(f'table {schema}.{table_raw_name} not found')
                return (None, None)
            if table_type == 'TABLE':
                col_query = f'''
                  select
                    TC.POSITION,
                    fieldname,
                    CASE WHEN TC.DATA_TYPE_NAME IN ('DECIMAL') THEN TC.DATA_TYPE_NAME || '('||LENGTH||','||SCALE||')'
                         WHEN TC.DATA_TYPE_NAME IN ('FLOAT','VARCHAR', 'NVARCHAR', 'ALPHANUM', 'SHORTTEXT','VARBINARY') THEN TC.DATA_TYPE_NAME || '('||LENGTH||')'
                    ELSE TC.DATA_TYPE_NAME END AS DATA_TYPE_NAME,
                    IS_NULLABLE,
                    ddtext
                from saphanadb.dd03l col
                    inner join table_columns tc on col.tabname = tc.table_name and col.FIELDNAME = tc.column_name and tc.table_oid = '{table_oid}'
                left join saphanadb.dd04t t on col.ROLLNAME = t.ROLLNAME and t.DDLANGUAGE = '1'
                where fieldname NOT like_REGEXPR '/'
                ORDER BY TC.POSITION
                ;
                '''
            else:  # view
                col_query = f'''
                SELECT POSITION,
                       COLUMN_NAME,
                       CASE
                           WHEN DATA_TYPE_NAME IN ('DECIMAL') THEN DATA_TYPE_NAME || '(' || LENGTH || ',' || SCALE || ')'
                           WHEN DATA_TYPE_NAME IN ('FLOAT', 'VARCHAR', 'NVARCHAR', 'ALPHANUM', 'SHORTTEXT', 'VARBINARY')
                               THEN DATA_TYPE_NAME || '(' || LENGTH || ')'
                           ELSE DATA_TYPE_NAME END AS DATA_TYPE_NAME,
                       IS_NULLABLE,
                       comments
                FROM VIEW_columns
                WHERE VIEW_OID = '{table_oid}'
                order by POSITION
                ;
                '''
            col_res = get_hana_query(col_query, _config=config)
        case 'SQLServer':
            col_ins_query = f'''
                            select
                            col.ORDINAL_POSITION ,
                            col.COLUMN_NAME,
                            col.data_type,
                            upper(col.COLUMN_NAME) as target_column_name,
                            case
                                when col.DATA_TYPE in ('nvarchar', 'varchar', 'char') then upper(col.DATA_TYPE)
                                when col.DATA_TYPE in ('decimal', 'numeric') then 'DECIMAL'
                                when col.DATA_TYPE in ('datetime2') then 'datetime'
                                when col.DATA_TYPE in ('int') then 'INTEGER'
                                when col.DATA_TYPE IN ('ntext' , 'xml') then 'nclob'
                                when col.DATA_TYPE = 'bit' then 'BOOLEAN'
                                --	 when t.name = 'varbinary' then 'BLOB'
                                else col.DATA_TYPE
                            end as target_data_type
                        from
                            {db_dict['dbName']}.INFORMATION_SCHEMA.COLUMNS col
                        where
                            upper(col.TABLE_NAME)= upper('{table_raw_name}')
                            AND 
                            lower(col.TABLE_SCHEMA) = lower('{schema}')
                        order by
                            ORDINAL_POSITION
                         '''
            col_res = get_mssql_query(col_ins_query)
        case 'MySQL':
            col_ins_query = f'''
                            select
                            col.ORDINAL_POSITION ,
                            col.COLUMN_NAME,
                            col.data_type,
                            upper(col.COLUMN_NAME) as target_column_name,
                            case
                                when col.DATA_TYPE in ('nvarchar', 'varchar', 'char') then upper(col.DATA_TYPE)
                                when col.DATA_TYPE in ('decimal', 'numeric') then 'DECIMAL'
                                when col.DATA_TYPE in ('datetime2') then 'datetime'
                                when col.DATA_TYPE in ('int') then 'INTEGER'
                                when col.DATA_TYPE IN ('longtext','ntext' , 'xml') then 'nclob'
                                when col.DATA_TYPE = 'bit' then 'BOOLEAN'
                                --	 when t.name = 'varbinary' then 'BLOB'
                                else upper(col.DATA_TYPE)
                            end as target_data_type
                        from
                            INFORMATION_SCHEMA.COLUMNS col
                        where
                            upper(col.TABLE_NAME)= upper('{table_raw_name}')
                         '''
            col_res = get_mysql_query(col_ins_query,db_dict)
        case 'Oracle':
            col_query = f'''
        SELECT 
        c.column_id,
        c.column_name,
       data_type,
       c.NULLABLE,
       comments
        FROM all_tab_cols c
                 LEFT JOIN all_col_comments cc
                           ON c.table_name = cc.table_name AND c.column_name = cc.column_name
        WHERE c.table_name = '{table_raw_name}'
        AND c.owner = '{schema}'
        ORDER BY c.column_id
        '''
            col_res = get_oracle_query(col_query, _config=config)



        case _:
            pass
    if not col_res:
        logger.error(f'{source_ds_id} {schema} {table_raw_name} 表不存在')
    return col_res
def get_hufu_sync_cols_mapping(task_id):
    mapping_query = f'''
    select
    source_field,
    target_field
    from hufu.hufu.tb_datadev_sync_field_mapping
    where node_id = {task_id}
    order by id
    '''
    col_res = get_pg_query(mapping_query)
    src_cols,tgt_cols = [],[]
    if not col_res:
        return (None, None)
    for src_col,tgt_col in col_res:
        src_cols.append(src_col)
        tgt_cols.append(tgt_col)
    return (','.join(src_cols),','.join(tgt_cols))

def create_sync_hufu(source_table, target_table) -> str:
    '''
    虎符创建数据同步任务:
    step1 : 创建空task
        1. 定位到对应的工作流id get_task_flow_id() -> task_flow_id,max_task_id
        2. 创建空节点
            1. 创建节点id menu_id hufu.tb_datadev_menunode
            2. 创建画布记录 sub_id HUFU.tb_datadev_sub_node
                1. 定位工作流画布位置，查找最短路径
            3. 创建连接线id HUFU.tb_datadev_sub_node_dependency
    step2 : 创建同步细节
        1. 表同步记录 hufu.tb_datadev_offline_sync sync
        2. 字段同步记录 tb_datadev_sync_field_mapping
    :param source_table:  需要同步的源表
    :param target_table:  需要同步的目标表名
    :return:
    '''
    table_info = source_table.split('.')
    if len(table_info) == 3 :
        db,schema,table_name = table_info
    else:
        db,table_name = table_info
    target_table = target_table.upper()
    id = get_taskflow_id(db)
    # id = 6126
    node_id = create_task_node(id, source_table, target_table)
    return node_id


if __name__ == '__main__':
    source_table = 'bpmdb.dbo.CF_IT_PermissionOpen_NV_ADGroup'
    target_table = 'CFBPM.ODS_CF_IT_PermissionOpen_NV_ADGroup'
    id = 39
    rs = get_dsconfig_by_id(id)
    logger.info(f'{rs}')
    # 测试
