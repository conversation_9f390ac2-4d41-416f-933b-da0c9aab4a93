# -*- coding: utf-8 -*-
import logging
import sys
import duckdb
from cachetools import TTLCache, cached
from itertools import permutations
import re
from sqllineage.core.metadata.sqlalchemy import SQLAlchemyMetaDataProvider
from sqllineage.core.parser import sqlparse
from sqllineage.runner import LineageRunner

from config import get_config
from open_metadata.ingest import add_lineage, add_lineage_by_query
from utils import get_logger
from utils.db_utils import get_mysql_query, get_pg_query, buffer_cache_get, buffer_cache_put, get_hana_query, \
    get_metadata_query
from sqlglot import parse_one, exp

logger = get_logger(__name__)
ttl_cache = TTLCache(maxsize=100, ttl=36000)

@cached(ttl_cache)
def get_full_table_name(src_name, table_name):
    match src_name:
        case 'h4p-sap':
            table_name = f'{src_name}.H4P.{table_name}'
            return table_name
        case 'data-center':
            table_name = f'{src_name}.CFD.{table_name}'
            return table_name
        case _:
            return f'{src_name}.{table_name}'

@cached(ttl_cache)
def get_xl_sync_list(src, tgt):
    os_dict = {
        'bpm': {
            17, 119, 82, 40, 8, 100, 19, 6, 101, 99
        },
        'FR': {-1, 15},
        'h4p-sap': {10, 37},
        'mid_cfdata': {-1, 42},
        'SRM': {-1, 109},
        'uip': {-1, 72},
        'data-center': {62, 39, 69, 63, 68},
        'cfdms': {-1,35,124,5,2},
        'qis':{-1,50},
        'bc':{-1,26},
        'APSPRD':{120,121},
        'H4D':{14,29,55}
    }
    src_id = tuple(os_dict[src])
    tgt_id = tuple(os_dict[tgt])
    query = '''
        select
        case when source_ds_id =39 and position('.' in source_table) = 0 then concat_ws('.',json(ss.rdbms_config_info) ->> 'schema',source_table) 
             when ss.ds_type = 'SQLServer' then concat_ws('.',json(ss.rdbms_config_info) ->> 'dbName',case when position('.' in source_table) = 0 then concat_ws('.','dbo',source_table) else source_table end)
             when ss.ds_type = 'PostgreSQL' then concat_ws('.',json(ss.rdbms_config_info) ->> 'dbName',case when position('.' in source_table) = 0 then concat_ws('.',json(ss.rdbms_config_info) ->> 'schema',source_table) else source_table end)
         --    when ss.ds_type = 'MySQL' and source_ds_id = 26 then concat_ws('.','default',json(ss.rdbms_config_info) ->> 'dbName',source_table)
             when ss.ds_type = 'MySQL' then concat_ws('.','default',json(ss.rdbms_config_info) ->> 'dbName',source_table)
        else source_table end source_table,
        case when target_ds_id =39 and position('.' in target_table) = 0  then concat_ws('.',json(st.rdbms_config_info) ->> 'schema',target_table) 
             when st.ds_type = 'SQLServer' then concat_ws('.',json(st.rdbms_config_info) ->> 'dbName',case when position('.' in target_table) = 0 then concat_ws('.','dbo',target_table) else target_table end)
             when st.ds_type = 'PostgreSQL' then concat_ws('.',json(st.rdbms_config_info) ->> 'dbName',case when position('.' in target_table) = 0 then concat_ws('.',json(st.rdbms_config_info) ->> 'schema',target_table) else target_table end)
          --   when st.ds_type = 'MySQL' and target_ds_id = 26 then concat_ws('.','default',json(st.rdbms_config_info) ->> 'dbName',target_table)
             when st.ds_type = 'MySQL' then concat_ws('.','default',json(st.rdbms_config_info) ->> 'dbName',target_table)
        else target_table end target_table,
        string_agg(job_name,'\n') as job_desc
    from
        hufu.tb_datadev_offline_sync sync
    inner join hufu.tb_datasource ss on
        sync.source_ds_id = ss.id 
    inner join hufu.tb_datasource st on
        sync.target_ds_id = st.id
    left join (
        select
            task.id as id,
            concat_ws('==>',
            tf.name,
            task.name) as job_name
        from
            hufu.tb_datadev_menunode tf
        left join HUFU.tb_datadev_sub_node n on
            tf.id = n.task_flow_id
            and n.row_state = 1
            and n.row_state = 1
        left join hufu.tb_datadev_menunode task on
            n.node_id = task.id
            and task.row_state = 1
        where
            task.node_type = 20
    )task on sync.node_id = task.id
    where
        source_ds_id in {0}
        and target_ds_id in {1}
    group by case when source_ds_id =39 and position('.' in source_table) = 0 then concat_ws('.',json(ss.rdbms_config_info) ->> 'schema',source_table) 
             when ss.ds_type = 'SQLServer' then concat_ws('.',json(ss.rdbms_config_info) ->> 'dbName',case when position('.' in source_table) = 0 then concat_ws('.','dbo',source_table) else source_table end)
             when ss.ds_type = 'PostgreSQL' then concat_ws('.',json(ss.rdbms_config_info) ->> 'dbName',case when position('.' in source_table) = 0 then concat_ws('.',json(ss.rdbms_config_info) ->> 'schema',source_table) else source_table end)
           --  when ss.ds_type = 'MySQL' and source_ds_id = 26 then concat_ws('.','default',json(ss.rdbms_config_info) ->> 'dbName',source_table)
             when ss.ds_type = 'MySQL' then concat_ws('.','default',json(ss.rdbms_config_info) ->> 'dbName',source_table)
        else source_table end,
        case when target_ds_id =39 and position('.' in target_table) = 0  then concat_ws('.',json(st.rdbms_config_info) ->> 'schema',target_table) 
             when st.ds_type = 'SQLServer' then concat_ws('.',json(st.rdbms_config_info) ->> 'dbName',case when position('.' in target_table) = 0 then concat_ws('.','dbo',target_table) else target_table end)
             when st.ds_type = 'PostgreSQL' then concat_ws('.',json(st.rdbms_config_info) ->> 'dbName',case when position('.' in target_table) = 0 then concat_ws('.',json(st.rdbms_config_info) ->> 'schema',target_table) else target_table end)
             -- when st.ds_type = 'MySQL' and target_ds_id = 26 then concat_ws('.','default',json(st.rdbms_config_info) ->> 'dbName',target_table)
             when st.ds_type = 'MySQL' then concat_ws('.','default',json(st.rdbms_config_info) ->> 'dbName',target_table)
        else target_table end 
    '''.format(src_id, tgt_id)
    res = get_pg_query(query)
    res_set = set()
    for source_table, target_table, job_desc in res:
        res_set.add(
            (get_full_table_name(src, source_table.upper()), get_full_table_name(tgt, target_table.upper()), job_desc))
    return list(res_set)

# @cached(ttl_cache)
def get_table_id(table_full_name,_no_cache=True):
    if not _no_cache:
        _cache_key = f'get_table_id:{table_full_name}'
        res = buffer_cache_get(_cache_key)
        if buffer_cache_get(_cache_key):
            return res
    src_name = table_full_name.split('.')[0]
    tempate_sql = '''
    select id
    from table_entity te
    where split_part(json ->> 'fullyQualifiedName','.',1) = '{0}'
    and upper(json ->> 'fullyQualifiedName') = upper('{1}')
    '''.format(src_name, table_full_name)
    _res = get_metadata_query(tempate_sql)
    if _res:
        buffer_cache_put(f'get_table_id:{table_full_name}', _res[0][0])
        return _res[0][0]
    else:
        return None

# hufu datax数据同步任务血缘
def add_datax_lineage():
    sc_list = {'bpm', 'FR', 'h4p-sap', 'mid_cfdata', 'SRM', 'data-center', 'bc', 'qis', 'cfdms', 'APSPRD', 'H4D'}
    # sc_list = {'mid_cfdata',  'data-center','cfdms'}
    sync_list = list(permutations(sc_list,2))
    for src,tgt in sync_list:
        tid = get_xl_sync_list(src, tgt)
        for src_table, tgt_table, job_desc in tid:
            sid = get_table_id(src_table)
            tid = get_table_id(tgt_table)
            if sid and tid:
                add_lineage(sid, 'table', tid, 'table', job_desc)
                logger.info(f'{src_table}||{sid} -> {tgt_table}||{tid} {job_desc}    Done!')
            else:
                logger.warning(f'{src_table}||{sid} -> {tgt_table}||{tid} Failed!')

# 从虎符里面读取数据，获取血缘信息，并添加到openmetadata中
def add_sc_lineage():
    '''
    1. 通过SQL读取虎符里面的Squery脚本信息
    2. 通过openmetadata的add_lineage_by_query添加血缘
    :return:
    '''
    sc_query = f'''
            select 
        tf.name,
        task.name,
        sc.script 
        from hufu.tb_datadev_menunode tf
        left join HUFU.tb_datadev_sub_node n on tf.id = n.task_flow_id and n.row_state  = 1 and n.row_state =1 
        left join hufu.tb_datadev_menunode task on n.node_id  = task.id and task.row_state  =1 
        inner join hufu.tb_datadev_script sc on task.id = sc.node_id and task.node_type =13
        inner join (
        select down_node_id 
        from HUFU.tb_datadev_sub_node_dependency
        where row_state =1 
        )v on task.id = v.down_node_id
        where tf.node_type = 1 
         and tf.row_state =1 
         and  tf.is_locked=0
         and script is not null 
        '''
    res = get_pg_query(sc_query)
    for re in res:
        task_flow_name, task_name, script = re
        script = f'''
       ---- {task_flow_name} ----
       ----  {task_name} ----
       {script}
    '''
        try:
            add_lineage_by_query(service='data-center', query=script)
            logger.info(f'{task_flow_name}||{task_name} {script}    Done!')
        except Exception as e:
            logger.error(f'{task_flow_name}||{task_name} {script} Failed!')
            logger.error(e)
def add_sap_lineage(service_name):
    SRC_SET = {'sap-h4p','data-center'}
    if service_name not in SRC_SET:
        return None


def remove_sql_comments(sql_text):
    """
    移除SQL脚本中的注释内容，支持处理：
    - 单行注释（--）
    - 多行注释（/*...*/）
    - 保留字符串内的注释符号（单引号/双引号/反引号包裹的内容）

    参数：
        sql_text (str): 原始SQL文本

    返回：
        str: 去除注释后的干净SQL
    """
    cleaned = []
    buffer = []
    i = 0
    n = len(sql_text)

    # 使用单一状态变量管理上下文
    context = 'CODE'  # 可能状态：CODE（代码）, '（单引号）, "（双引号）, `（反引号）, --（单行注释）, /*（多行注释）

    while i < n:
        char = sql_text[i]
        next_char = sql_text[i+1] if i+1 < n else ''

        # 处理字符串上下文
        if context == 'CODE':
            if char == "'":
                context = "'"
                buffer.append(char)
            elif char == '"':
                context = '"'
                buffer.append(char)
            elif char == '`':
                context = '`'
                buffer.append(char)
            elif char == '-' and next_char == '-':
                context = '--'
                i += 1  # 跳过第二个-
            elif char == '/' and next_char == '*':
                context = '/*'
                i += 1  # 跳过*
            else:
                buffer.append(char)

        # 处理单行注释
        elif context == '--':
            if char == '\n':
                buffer.append('\n')  # 保留换行符
                context = 'CODE'

        # 处理多行注释
        elif context == '/*':
            if char == '*' and next_char == '/':
                context = 'CODE'
                i += 1  # 跳过/

        # 处理字符串（统一处理转义字符）
        elif context in ("'", '"', '`'):
            buffer.append(char)
            # 处理转义字符
            if char == '\\':
                # 保留转义序列，下个字符直接追加
                if i+1 < n:
                    i += 1
                    buffer.append(sql_text[i])
            elif char == context:
                # 结束字符串需要确保不是转义字符
                if buffer[-2:] != ['\\', context]:  # 排除转义情况
                    context = 'CODE'

        i += 1

    return ''.join(buffer).strip()





def extract_table_name_safe(sql_statement):
    if not isinstance(sql_statement, str) or not sql_statement.strip():
        return None
    try:
        # 使用正则表达式匹配DML的目标表名
        pattern = re.compile(
            r"""
            \b(?:INSERT|MERGE)\s+INTO\s+([\w."`'`]+)  # 匹配INSERT/MERGE INTO的表名
            |                                               # 或者
            \b(?:UPDATE|UPSERT)\s+([\w."`'`]+)                         # 匹配UPDATE的表名
            """,
            re.IGNORECASE | re.VERBOSE
        )
        match = pattern.search(sql_statement)
        if match:
            # 优先返回INSERT/UPSERT/MERGE的匹配结果
            if match.group(1):
                return match.group(1).strip()
            else:
                return match.group(2).strip()
        return None
    except Exception as e:
        logger.error(f"提取表名失败: {str(e)}")
        return None


def update_hufu_sc_ddl():
    '''
   1. 通过SQL读取虎符里面的Squery脚本信息
   2. query脚本分段处理
   3. 识别表信息和DDL信息，插入
   :return:
   '''
    sc_query = f'''
                select 
            tf.name,
            task.name,
            sc.script 
            from hufu.tb_datadev_menunode tf
            left join HUFU.tb_datadev_sub_node n on tf.id = n.task_flow_id and n.row_state  = 1 and n.row_state =1 
            left join hufu.tb_datadev_menunode task on n.node_id  = task.id and task.row_state  =1 
            inner join hufu.tb_datadev_script sc on task.id = sc.node_id and task.node_type =13
            inner join (
            select down_node_id 
            from HUFU.tb_datadev_sub_node_dependency
            where row_state =1 
            )v on task.id = v.down_node_id
            where tf.node_type = 1 
             and tf.row_state =1 
             and  tf.is_locked=0
             and script is not null 
            '''
    res = get_pg_query(sc_query)
    for rs in res:
        task_flow_name, task_name, script = rs
        # 去掉SQL脚本所有注释
        script_no_commit  = remove_sql_comments(script)
        for sql_seg in script_no_commit.split(';'):
            if sql_seg.strip() and ('insert' in sql_seg.lower() or 'update' in sql_seg.lower() or 'upsert' in sql_seg.lower() or 'merge' in sql_seg.lower()):
                try:
                    table_name = extract_table_name_safe(sql_seg)

                    if table_name:
                        table_name_fix = table_name.replace('"', '').upper()
                        insert_sql = f'upsert cfmeta.hufu_script_meta VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP) where fworkflow = ? and ftaskname = ? and ftablename = ?'
                        params = (task_flow_name, task_name, table_name_fix, sql_seg,'',task_flow_name, task_name, table_name_fix)
                        get_hana_query(insert_sql,params=params)

                    else:
                        logger.error(f'{sql_seg.strip()} Failed!')
                    logger.info(sql_seg.strip())
                    logger.info(f'{task_flow_name}||{task_name} {sql_seg}    Done!')
                except Exception as e:
                    logger.error(f'{task_flow_name}||{task_name} {sql_seg} Failed!')
                    logger.error(e)

def add_hana_view_lineage(data_source):
    if data_source not in ('data-center', 'h4p-sap'):
        return None
    db_config = get_config()
    if data_source == 'data-center':
        ds_config = db_config['db.cfd']
        view_sql = f'''
        select
    'create view ' || schema_name||'.'||view_name || '  as '|| definition
            from views
        where schema_name like 'CF%'
        '''
    else:
        ds_config = db_config['db.h4p']
        view_sql = f'''
        select
    'create view ' || schema_name||'.'||view_name || '  as '|| definition
            from views
        where schema_name in ('SAPHANADB','CFDBUSER')
        '''
    res = get_hana_query(view_sql, _config=ds_config)
    for rs in res:
        view_sql = rs[0]
        add_lineage_by_query(data_source, view_sql)



if __name__ == '__main__':
    logger.setLevel(logging.INFO) # 设置日志级别
    ds_name = 'data-center'
    table_full_name = 'CFD.cfdata.cf_bpm_nearexpi_purprice_warn'
    src_name = 'SRM'
    tgt_name = 'data-center'
    tid = get_xl_sync_list(src_name, tgt_name)
    add_datax_lineage()
    add_sc_lineage()
    update_hufu_sc_ddl()
    # add_hana_view_lineage('h4p-sap')
    # add_hana_view_lineage('data-center')
    # extract_table_name_safe('insert into "CFD"."cfdata"."cf_bpm_nearexpi_purprice_warn" values (1,2,3)')
