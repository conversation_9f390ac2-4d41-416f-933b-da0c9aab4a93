import dataclasses
import os
import re
from enum import EnumType
from typing import Set, List
from urllib.parse import urljoin, urlparse

import bs4
from cachetools import TTLCache, cached

from config import get_config
from fine_report import FINE_CPT_BASE, FINE_BASE_URL, BPM_TASK_URL
from open_metadata.ingest import dashboad_datamodel_add,dashboad_add,add_lineage
from utils import get_logger
from open_metadata import DataModel,Dashboard
from utils.sql_table_extractor import extract_tables_by_schema, extract_all_tables_from_sql

from utils.db_utils import get_mssql_query, get_hana_query, get_mysql_query

logger = get_logger(__name__)
db_config = get_config()
fr_config = db_config['db.fr']
metadata_config = db_config['db.openmeta']
ttl_cache = TTLCache(maxsize=100, ttl=36000)





@cached(ttl_cache)
def get_cpt_list_from_base_dir(dir) -> set:
      if not os.path.isdir(dir):
        return set()
      rs = set()

      with os.scandir(dir) as it:
          for entry in it:
              if not entry.name.startswith('.') and entry.is_file() and (entry.name.endswith('.cpt') or entry.name.endswith('.frm')):
                  rpt_full_name = os.path.join(dir, entry.name)
                  rs.add(rpt_full_name)
                  # logger.info(rpt_full_name)
              elif not entry.name.startswith('.') and entry.is_dir():
                  dir_full_name = os.path.join(dir, entry.name)
                  rs1 = get_cpt_list_from_base_dir(dir_full_name)
                  if rs1:
                    rs = rs | rs1
                  # logger.info(f'{entry.name} is directory')

      return rs

@cached(ttl_cache)
def get_datamodel_from_cpt(cpt_file) -> list:
    if not os.path.isfile(cpt_file):
        return []
    rpt_name = os.path.basename(cpt_file)
    rs = []
    with open(file=cpt_file, mode='r', encoding='utf-8') as f:
        soup = bs4.BeautifulSoup(f, 'lxml-xml')
        if not soup.TableDataMap:
            return []
        data_models = soup.TableDataMap.find_all('TableData')
        if data_models and len(data_models) > 0:
            for data_model in data_models:
                ds_name = data_model['name']
                query = ''
                if data_model.Query:
                    query = data_model.Query.text
                if data_model.DatabaseName:
                    db_name = data_model.DatabaseName.text
                else:
                    db_name = ''
                dep_service = get_service_by_dsname(db_name)
                logger.info(f'model: {ds_name} dep_service:{dep_service}')
                para_dict = dict()
                if data_model.Parameters:
                    paras = data_model.Parameters.find_all('Parameter')
                    for para in paras:
                        name = para.Attributes['name']
                        value = para.O.text.replace('\n', '')
                        para_dict[name] = value
                pattern = re.compile(r'\$\{.+\}')
                re_query = re.sub(pattern, r'', query)
                om_datamodel = DataModel(

                name= f'{rpt_name.split('.')[0]}_{ds_name}',
                    service='FR-report',
                    dataModelType='PowerBIDataModel',
                    columns=[],
                    description= f'数据集名称：{ds_name} \n 数据源： {db_name} \n 数据集查询： {re_query} \n 参数列表:{para_dict}',
                    displayName= f'{rpt_name.split('.')[0]}.{ds_name}',
                    sql= f'{query}'
                )
                rs.append(om_datamodel)
    return rs
                # logger.info(f'数据集名称：{ds_name} \n 数据源： {db_name} \n 数据集查询： {re_query} \n 参数列表:{para_dict}')

@cached(ttl_cache)
def get_dirname_from_id(id) -> str:

    if id == 'decision-directory-root':
        return ''
    query = f'''
            select convert(nvarchar,displayname)
            from
            FRDB.DBO.FINE_AUTHORITY_OBJECT
            where id  = '{id}'
            and expandType = '3'
            '''
    rs = get_mssql_query(query,fr_config)
    if rs:
        return rs[0][0]
    return ''

@cached(ttl_cache)
def get_mount_url(tpl_name) -> str:

    mount_url_query = f'''
    select fullPath, convert(nvarchar, displayName) as displayName
        from FRDB.DBO.FINE_AUTHORITY_OBJECT
        where expandType = '102'
        and displayName  not like '%帆软Demo%'
        and path = '{tpl_name}'
        ;
    '''

    mount_url_rs = get_mssql_query(mount_url_query,fr_config)
    if not mount_url_rs:
        exec_mount_url_query = f'''
                                select  distinct
                                    a1.entry
                                from cffr.ods_INF_ROLE_ENTRY a1
                                WHERE a1.entry not like '%帆软Demo%'
                                and a1.TEMPLATE_URL = '{tpl_name}'
                                '''
        mount_url_rs = get_hana_query(exec_mount_url_query)
        if mount_url_rs:
            return ','.join([it[0] for it in mount_url_rs])
        else:
            return ''
    mount_url_set = set()
    for fullpath,display_name in mount_url_rs:
        path_list = fullpath.split('-_-')[1:]
        path_name_list = [get_dirname_from_id(path) for path in path_list]
        logger.info(f'path:{'/'.join(path_name_list)}  displayName:{display_name}')
        murl = f'{'/'.join(path_name_list)}/{display_name}'
        mount_url_set.add(murl)
    return ','.join(mount_url_set)

@cached(ttl_cache)
def get_service_by_dsname(dsname):
    dbname = dsname.strip()
    fr_query = f'''
           select convert(nvarchar(100),value)
from dbo.fine_conf_entity
where id = 'ConnectionConfig.connections.{dbname}.url'
                '''
    fr_url = get_mssql_query(fr_query,fr_config)
    if fr_url:
        if not '://' in fr_url[0][0]:
            return ''
        url_parse = urlparse(fr_url[0][0]).path.split('://')[1]
        if ':' in url_parse:
            host_name,port =  url_parse.split(':')
        else:
            host_name = url_parse
            port = 0
        if host_name == '************':
            host_name = '*************'

        service_query = f'''
            select
                name,
                serviceType,
                json,
                coalesce(json ->> '$.connection.config.hostPort',json ->> '$.connection.config.connection.hostPort') as host_port ,
                coalesce(json ->> '$.connection.config.username', json ->> '$.connection.config.connection.username') as username
            from dbservice_entity
            where coalesce(json ->> '$.connection.config.hostPort',json ->> '$.connection.config.connection.hostPort') = '{host_name}:{port}'
            ;
            '''
        service = get_mysql_query(service_query,metadata_config)
        if service:
            return service[0][0]
    return None

def get_rpt_info(tpl_name,model_list=[]) -> Dashboard:
    if not tpl_name:
        return {}
    fix_tpl = os.path.relpath(tpl_name,FINE_CPT_BASE)
    nomal_url = fix_tpl.replace('\\','/')
    url_tpl = ''.join([FINE_BASE_URL, nomal_url])
    pv_query = f'''
    SELECT COUNT(*) AS qcnt,
          to_char(min(add_seconds(to_timestamp('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS'),time / 1000)),'yyyy-MM-dd') as first_visit_time,
          to_char(max(add_seconds(to_timestamp('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS'),time / 1000)),'yyyy-MM-dd') as first_visit_dt
          FROM cffr.ODS_FINE_RECORD_EXECUTE
          WHERE  ip not in ('*************','*************')
          and ( username not  like_regexpr '孙睿|张俊飞|张飞|马阳阳' or username is null )
          and tname = '{nomal_url}'
    '''
    pv_res = get_hana_query(pv_query)
    if pv_res:
        pv,upload_dt,last_visit_dt = pv_res[0]
    else:
        return {}
    if pv < 10:
        return {}
    mount_url_rs = get_mount_url(nomal_url)
    mount_url = ''
    if mount_url_rs:
        for  url_rs in mount_url_rs:
            mount_url += url_rs[0]

    bpm_info_query = f'''
                select distinct
                        a3.taskid
            from cffr.dwd_fine_record_operate a1
                left join CFFR.FRM_INF_LOG_SJ a2 on a1.SYNCID=a2.SYNCID
                inner join
                (
                select sn,taskid,createuserid,frequireproblem,frequirehope from CFBPM.ODS_CF_IT_REQUIREMENTSFORBPM_P
                union all
                select sn,taskid,createuserid,fproblem,fhope from cfbpm.ods_cf_it_requirementsforsap_p
                )a3 on a2.sn=a3.sn
                left join CFHR.DIM_PS_EMP a4 on ifnull(a2.SQNAME,a3.createuserid)=a4.fempno
                left join	(
                select
                substr(a1.tname,instr(a1.tname,'/',-1)+1,instr(a1.tname,'.',1)-instr(a1.tname,'/',-1)-1) as url,
                a1.tname
                from cffr.ODS_FINE_RECORD_EXECUTE a1
                where username is not null
                ) a5 on substr(a1.operateresource,instr(a1.operateresource,'/',-1)+1)=a5.url
            where a1.operateType='Dec-Platform_Directory_Manage'
              and a1.operate='Dec-Log_Add'
              and a2.iftrue='true'
              and a5.tname = '{nomal_url}'
            '''
    bpm_task_rs =  get_hana_query(bpm_info_query)
    bpm_task = ''
    if  bpm_task_rs:
        bpm_task = f'{BPM_TASK_URL}{bpm_task_rs[0][0]}'
    logger.info(f'{nomal_url}: 访问量{pv}  挂载url:{mount_url} 上架时间：{upload_dt} 最后访问时间:{last_visit_dt} bpm:{bpm_task}')
    return Dashboard(
    name =  nomal_url,
    service = 'FR-report',
    dashboardType='Report',
    dataModels = model_list,
    description = f'{nomal_url}: \n访问量{pv}  \n挂载url:{mount_url} \n上架时间：{upload_dt} \n最后访问时间:{last_visit_dt} \nbpm链接:{bpm_task}',
    displayName =  nomal_url,
    sourceUrl = f'{FINE_BASE_URL}{nomal_url}'
    )




def extract_dm_sql_tables(sql: str, schema: str = 'cfdms') -> Set[str]:
    """
    Extract all referenced tables from SQL in a DM (Data Model) and collect tables with the specified schema into a set.

    Args:
        sql (str): The SQL query to parse
        schema (str, optional): The schema name to filter by. Defaults to 'cfdms'.

    Returns:
        Set[str]: A set of table names with the specified schema
    """
    if not sql or not sql.strip():
        return set()

    # Extract tables with the specified schema
    schema_tables = extract_tables_by_schema(sql, schema)

    return schema_tables


def extract_cpt_tables(cpt_file: str, schema: str = 'cfdms') -> Set[str]:
    """
    Extract all referenced tables from all data models in a report file and collect tables with the specified schema into a set.

    Args:
        cpt_file (str): Path to the report file (.cpt or .frm)
        schema (str, optional): The schema name to filter by. Defaults to 'cfdms'.

    Returns:
        Set[str]: A set of table names with the specified schema
    """
    if not os.path.isfile(cpt_file):
        return set()

    # Get all data models from the report file
    data_models = get_datamodel_from_cpt(cpt_file)

    # Initialize an empty set to store tables
    schema_tables = set()

    # Extract tables from each data model's SQL
    for dm in data_models:
        # Access the sql attribute directly based on the DataModel class structure
        if hasattr(dm, 'sql') and dm.sql:
            dm_tables = extract_dm_sql_tables(dm.sql, schema)
            schema_tables.update(dm_tables)

    return schema_tables


if __name__ == "__main__":
    table_set = set()
    rpt_set = get_cpt_list_from_base_dir(FINE_CPT_BASE)
    for rpt in rpt_set:
        rpt_s = os.path.basename(rpt)
        test_pattern = r'test|back|20[12][891234][12][1-9]|副本|WorkBook|案例|demo'
        # 过滤测试备份类报表
        if re.search(test_pattern, rpt_s):
            logger.info(rpt_s)
            continue
        ext_set = extract_cpt_tables(rpt)
        table_set.update(ext_set)
    #     dm_set = get_datamodel_from_cpt(rpt)
    #     model_list = [f'FR-report.model.{dm.name}' for dm in dm_set]
    #     id_list = []
    #     rpt_info = get_rpt_info(rpt,model_list)
    #     if rpt_info:
    #         for dm in dm_set:
    #             logger.info(dm.name)
    #             id =  dashboad_datamodel_add(dm)
    #             if id:
    #                 id_list.append(id)
    #                 logger.info(f'id:{id}')
    #         dashboard_id = dashboad_add(rpt_info)
    #         for id in id_list:
    #             add_lineage(id,'dashboardDataModel',dashboard_id,'dashboard')
    #         # get_rpt_info(rpt)
    # logger.info(f'总计报表模板数： {len(rpt_set)}')
    # # Example usage of the table extraction functions
    # # test_rpt = r'D:\work\projects\cffr\CFMOTO/SAP/SAP整车对账单.cpt'
    # # if os.path.isfile(test_rpt):
    # #     # Extract all cfdms tables from the report
    # #     cfdms_tables = extract_cpt_tables(test_rpt, 'cfdms')
    # #     logger.info(f'CFDMS tables in report: {cfdms_tables}')
    # #
    # #     # Extract tables with a different schema
    # #     other_tables = extract_cpt_tables(test_rpt, 'other_schema')
    # #     logger.info(f'Other schema tables in report: {other_tables}')
    # #
    # #     # Get all data models and extract tables from each SQL
    # #     dm_set = get_datamodel_from_cpt(test_rpt)
    # #     for dm in dm_set:
    # #         if hasattr(dm, 'sql') and dm.sql:
    # #             all_tables = extract_all_tables_from_sql(dm.sql)
    # #             logger.info(f'All tables in {dm.name}: {all_tables}')
    # #
    # #             cfdms_tables = extract_dm_sql_tables(dm.sql)
    # #             logger.info(f'CFDMS tables in {dm.name}: {cfdms_tables}')
    print(table_set)
