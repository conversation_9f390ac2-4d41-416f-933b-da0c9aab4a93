from config import get_config
from utils import get_logger
from utils.db_utils import get_mssql_query, get_hana_query, get_mysql_query

db_config = get_config()
primary_config = db_config['db.phana']
sec_config = db_config['db.shana']

if __name__ == '__main__':
    logger = get_logger(__name__)
    sql = "select schema_name||'.\"'||table_name||'\"' from tables where schema_name = 'CFDMS'"

    for rs in get_hana_query(sql, primary_config):
        table = rs[0]
        table_count_query = f'''
        select count(*)
        from {table}
        '''
        rs1 = get_hana_query(table_count_query, primary_config)
        table_count1 = rs1[0][0]
        rs2 = get_hana_query(table_count_query, sec_config)
        table_count2 = rs2[0][0]
        if table_count1 != table_count2:
            logger.error(f'''table: {table} count:{table_count1}''')
        else:
            logger.info(f'''table: {table} count:{table_count1}''')




