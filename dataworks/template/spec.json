{"version": "1.1.0", "kind": "Node", "spec": {"nodes": [{"rerunInterval": 300000, "autoparse": false, "script": {"path": "mcp/mcp_odps_sql", "runtime": {"commandTypeId": 23}}, "parameters": [{"artifactType": "Variable", "name": "bizdate", "scope": "NodeParameter", "type": "System", "value": "$bizdate"}], "RuntimeResource": {"ResourceGroup": "Serverless_res_group_329073075337474_729685086666945"}}], "trigger": {"type": "Scheduler", "cron": "00 10 00 ** ?", "cycleType": "Daily", "starTime": "1970-01-01 00:00:00", "endTime": "9999-01-01 00:00:00", "timezone": "Asia Shanghai", "delaySeconds": 0}}}