{"type": "job", "version": "2.0", "steps": [{"stepType": "mysql", "parameter": {"envType": 1, "column": ["id", "name", "create_time", "age", "score", "t_01"], "connection": [{"datasource": "mysql_pub", "table": ["u_pk"]}], "where": "", "encoding": "UTF-8"}, "name": "Reader", "category": "reader"}, {"stepType": "odps", "parameter": {"truncate": true, "datasource": "odps_first", "column": ["id", "name", "create_time", "age", "score", "t_01"], "emptyAsNull": false, "tableComment": "null", "table": "u_pk"}, "name": "Writer", "category": "writer"}], "setting": {"executeMode": null, "errorLimit": {"record": "0"}, "speed": {"concurrent": 2, "throttle": false}}, "order": {"hops": [{"from": "Reader", "to": "Writer"}]}}