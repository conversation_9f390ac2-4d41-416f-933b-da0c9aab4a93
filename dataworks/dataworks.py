import json
import os
import sys
from enum import Enum
from typing import List, Optional, Dict, Any, Tuple
import logging

from alibabacloud_dataworks_public20240518.client import Client as dataworks_public20240518Client
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dataworks_public20240518 import models as dataworks_public_20240518_models
from alibabacloud_dataworks_public20200518 import models as dataworks_public_20200518_models
from alibabacloud_dataworks_public20200518.client import Client as dataworks_public20200518Client
from alibabacloud_tea_openapi.client import Client as OpenApiClient
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_openapi_util.client import Client as OpenApiUtilClient
from json_templates import JsonTemplates
import pystache
import utils
from os import path

class D2:
    """DataWorks客户端类，用于管理DataWorks项目中的文件和任务"""

    credential = CredentialClient()
    config = open_api_models.Config(credential=credential)
    # Endpoint 请参考 https://api.aliyun.com/product/dataworks-public
    config.endpoint = 'dataworks.cn-hangzhou.aliyuncs.com'
    new_client = dataworks_public20240518Client(config)
    client = dataworks_public20200518Client(config)
    runtime = util_models.RuntimeOptions()

    class Scene(Enum):
        """场景枚举"""
        project = 'DataworksProject'  # 项目目录
        manual_workflow = 'DataworksManualWorkflow'  # 手动工作流
        manual_task = 'DataworksManualTask'  # 手动任务

    def __init__(self):
        """初始化D2客户端"""
        self.project_id = 322310  # cfbigdata
        self.auto_rerun_interval_millis = 5 * 60 * 1000  # 自动重跑间隔5分钟
        self.rerun_mode = 'ALL_ALLOWED'  # 运行失败重跑
        self.resource_group_identifier = 'Serverless_res_group_329073075337474_729685086666945'  # 调度资源组
        self.scheduler_type = 'NORMAL'
        self.create_folder_if_not_exists = True
        self.models = dataworks_public_20240518_models
        self.old_models = dataworks_public_20200518_models
        self.logger = utils.get_logger(__name__)

    @staticmethod
    def get_job_template(file_type: int) -> Dict[str, Any]:
        """获取作业模板

        Args:
            file_type: 文件类型

        Returns:
            作业模板字典
        """
        job_config = {
            23: 'di',  # 数据集成任务
            900: 'ri',
            24: 'odps_script',
            10012: 'saphana',
            99: 'virtual',
            0: 'spec',
        }
        template_file_name = f'template/{job_config[file_type]}.json'
        template_path = path.join(path.dirname(__file__), template_file_name)
        with open(template_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    @staticmethod
    def create_api_info(action: str) -> open_api_models.Params:
        """创建API信息

        Args:
            action: 接口名称

        Returns:
            OpenApi.Params对象
        """
        params = open_api_models.Params(
            action=action,
            version='2020-05-18',
            protocol='HTTPS',
            method='POST',
            auth_type='AK',
            style='RPC',
            pathname='/',
            req_body_type='formData',
            body_type='json'
        )
        return params

    def create_file(self, file_folder_path: str, file_name: str, file_type: str,
                   content: str, input_list: Optional[List[str]] = None,
                   connection_name: Optional[str] = None) -> Optional[str]:
        """创建文件

        Args:
            file_folder_path: 文件夹路径
            file_name: 文件名
            file_type: 文件类型
            content: 文件内容
            input_list: 输入列表
            connection_name: 连接名称

        Returns:
            文件ID，失败时返回None
        """
        create_file_request = self.models.CreateFileRequest(
            project_id=self.project_id,
            file_name=file_name,
            file_folder_path=file_folder_path,
            file_type=file_type,
            content=content,
            auto_rerun_times=3,
            auto_rerun_interval_millis=self.auto_rerun_interval_millis,
            resource_group_identifier=self.resource_group_identifier,
            create_folder_if_not_exists=self.create_folder_if_not_exists,
            scheduler_type=self.scheduler_type,
            rerun_mode=self.rerun_mode,
            input_list=','.join(input_list) if input_list else 'cfbigdata_root',
            auto_parsing=True,
            connection_name=connection_name
        )
        try:
            response = self.new_client.create_file_with_options(create_file_request, self.runtime)
            file_id = response.body.data
            self.logger.info(f'创建文件成功, fileId: {file_id}')
            return file_id
        except Exception as error:
            self.logger.error(f'创建文件失败: {error.message}')
            if hasattr(error, 'data') and error.data:
                self.logger.error(f'诊断地址: {error.data.get("Recommend", "")}')
            return None

    def create_workflow(self, work_flow_name: str) -> bool:
        """创建工作流

        Args:
            work_flow_name: 工作流名称

        Returns:
            创建是否成功
        """
        action = 'CreateBusiness'
        params = self.create_api_info(action)
        body = {
            "businessName": work_flow_name,
            "businessDesc": "test",
            "ProjectId": self.project_id
        }
        request = open_api_models.OpenApiRequest(body=body)

        try:
            response = self.client.call_api(params, request, self.runtime)
            self.logger.info('创建工作流成功')
            return True
        except Exception as e:
            self.logger.error(f'创建工作流失败: {e}')
            return False

    def create_di_task(self, input_list: Optional[List[str]], file_folder_path: str,
                      file_name: str, src_type: str, src_name: str, src_columns: str,
                      src_table: str, tgt_type: str, tgt_columns: str, tgt_table: str,
                      target_schema: str, target_database: str, is_truncate: bool = True,
                      where: Optional[str] = None) -> Optional[str]:
        """创建数据集成任务

        Args:
            input_list: 输入列表
            file_folder_path: 文件夹路径
            file_name: 文件名
            src_type: 源数据源类型
            src_name: 源数据源名称
            src_columns: 源字段列表
            src_table: 源表名
            tgt_type: 目标数据源类型
            tgt_columns: 目标字段列表
            tgt_table: 目标表名
            target_schema: 目标schema
            target_database: 目标数据库
            is_truncate: 是否截断表
            where: WHERE条件

        Returns:
            节点ID，失败时返回None
        """
        file_type = 23  # di
        if '.' in src_table:
            schema, table_name = src_table.split('.')
        else:
            table_name = src_table

        di_template = self.get_job_template(file_type)
        di_template['steps'][0]['stepType'] = src_type
        # 修复语法错误：正确设置datasource
        di_template['steps'][0]['parameter']['connection'][0]['datasource'] = src_name
        di_template['steps'][0]['parameter']['datasource'] = src_name
        di_template['steps'][0]['parameter']['column'] = src_columns.split(',')
        di_template['steps'][0]['parameter']['connection'][0]['table'] = table_name.split(',')

        if src_type == 'oracle':
            di_template['steps'][0]['parameter']['selectedDatabase'] = schema
            di_template['steps'][0]['parameter']['connection'][0]['table'] = src_table.split(',')

        if where:
            di_template['steps'][0]['parameter']['where'] = where

        di_template['steps'][1]['stepType'] = tgt_type
        di_template['steps'][1]['parameter']['datasource'] = target_database
        di_template['steps'][1]['parameter']['column'] = tgt_columns.split(',')
        di_template['steps'][1]['parameter']['table'] = tgt_table
        di_template['steps'][1]['parameter']['schema'] = target_schema
        di_template['steps'][1]['parameter']['tableComment'] = ''

        if tgt_type == 'odps':
            di_template['steps'][1]['parameter']['partition'] = 'ds=${bizdate}'
        elif tgt_type == 'saphana' and is_truncate:
            truncate_sql = f'truncate table {target_schema}.{tgt_table}'
            di_template['steps'][1]['parameter']['preSql'] = [truncate_sql]

        di_template['extend'] = {} if 'extend' not in di_template else di_template['extend']
        di_template['extend']['mode'] = 'wizard'
        di_template['extend']['resourceGroup'] = self.resource_group_identifier
        di_template['extend']['cu'] = 0.5
        content = json.dumps(di_template)

        spec = self.get_job_template(0)
        spec['spec']['nodes'][0]['script']['path'] = '/'.join([file_folder_path, file_name])
        spec['spec']['nodes'][0]['script']['content'] = content
        spec['spec']['nodes'][0]['script']['runtime']['cu'] = '0.5'
        spec['spec']['nodes'][0]['script']['runtime']['commandTypeId'] = 23
        spec['spec']['nodes'][0]['script']['runtime']['command'] = 'DI'
        spec['spec']['nodes'][0]['name'] = file_name

        # 修复输入输出逻辑错误
        spec['spec']['nodes'][0]['outputs'] = {}
        spec['spec']['nodes'][0]['outputs']['nodeOutputs'] = []
        out_put_name = f"{target_database}.{target_schema}.{tgt_table}"
        node_output = {"data": out_put_name.lower(), "refTableName": out_put_name.lower()}
        spec['spec']['nodes'][0]['outputs']['nodeOutputs'].append(node_output)

        spec_json = json.dumps(spec, ensure_ascii=False)
        return self.create_node(spec_json)

    def create_hana_task(self, file_folder_path: str, file_name: str, content: str) -> Optional[str]:
        """创建HANA任务

        Args:
            file_folder_path: 文件夹路径
            file_name: 文件名
            content: 文件内容

        Returns:
            文件ID，失败时返回None
        """
        file_type = 10012
        return self.create_file(file_folder_path, file_name, file_type, content,
                               input_list=None, connection_name='HDW_CFDATA')

    def create_di_task_old(self, file_folder_path: str, file_name: str, src_type: str,
                          src_name: str, src_columns: str, src_table: str, tgt_type: str,
                          tgt_columns: str, tgt_table: str, target_schema: str,
                          target_database: str, is_truncate: bool = True,
                          where: Optional[str] = None) -> Optional[str]:
        """创建数据集成任务（旧版本API）

        Args:
            file_folder_path: 文件夹路径
            file_name: 文件名
            src_type: 源数据源类型
            src_name: 源数据源名称
            src_columns: 源字段列表
            src_table: 源表名
            tgt_type: 目标数据源类型
            tgt_columns: 目标字段列表
            tgt_table: 目标表名
            target_schema: 目标schema
            target_database: 目标数据库
            is_truncate: 是否截断表
            where: WHERE条件

        Returns:
            文件ID，失败时返回None
        """
        para_config = {
            'FileFolderPath': file_folder_path,
            'ResourceGroup': self.resource_group_identifier,
            'Cu': 0.5
        }

        di_template = self.get_job_template(23)
        di_template['steps'][0]['stepType'] = src_type
        # 修复语法错误
        di_template['steps'][0]['parameter']['connection'][0]['datasource'] = src_name
        di_template['steps'][0]['parameter']['datasource'] = src_name
        di_template['steps'][0]['parameter']['column'] = src_columns.split(',')
        di_template['steps'][0]['parameter']['connection'][0]['table'] = src_table.split(',')

        if where:
            di_template['steps'][0]['parameter']['where'] = where

        di_template['steps'][1]['stepType'] = tgt_type
        di_template['steps'][1]['parameter']['datasource'] = target_database
        di_template['steps'][1]['parameter']['column'] = tgt_columns.split(',')
        di_template['steps'][1]['parameter']['table'] = tgt_table
        di_template['steps'][1]['parameter']['schema'] = target_schema
        di_template['steps'][1]['parameter']['tableComment'] = ''

        if tgt_type == 'odps':
            di_template['steps'][1]['parameter']['partition'] = 'ds=${bizdate}'
        elif tgt_type == 'saphana' and is_truncate:
            truncate_sql = f'truncate table {target_schema}.{tgt_table}'
            di_template['steps'][1]['parameter']['preSql'] = [truncate_sql]

        request = self.old_models.CreateDISyncTaskRequest(
            project_id=self.project_id,
            task_type='DI_OFFLINE',
            task_param=json.dumps(para_config, ensure_ascii=False),
            task_name=file_name,
            task_content=json.dumps(di_template, ensure_ascii=False)
        )

        try:
            response = self.client.create_disync_task_with_options(request, self.runtime)
            if response.status_code != 200:
                self.logger.error(f'创建文件失败, statusCode: {response.status_code}, error message: {response.body.message}')
                return None
            file_id = response.body.data.file_id
            self.logger.info(f'创建文件成功, fileId: {file_id}')
            return file_id
        except Exception as e:
            self.logger.error(f'创建文件失败, error message: {e}')
            return None

    def update_file(self, file_id: str, output_list: str) -> bool:
        """更新文件

        Args:
            file_id: 文件ID
            output_list: 输出列表

        Returns:
            更新是否成功
        """
        request = self.models.UpdateFileRequest(
            auto_rerun_times=3,
            project_id=self.project_id,
            auto_rerun_interval_millis=300000,
            rerun_mode=self.rerun_mode,
            stop=False,
            para_value='bizdate=$bizdate',
            cron_express='00 10 00 * * ?',
            cycle_type='DAY',
            input_list='cfbigdata_root',
            output_list=output_list,
            file_id=file_id,
            resource_group_identifier=self.resource_group_identifier,
            scheduler_type='NORMAL'
        )

        try:
            response = self.new_client.update_file_with_options(request, self.runtime)
            if response.status_code == 200:
                self.logger.info('更新文件成功')
                return True
            else:
                self.logger.error(f'更新文件失败, statusCode: {response.status_code}, error message: {response.body.message}')
                return False
        except Exception as e:
            self.logger.error(f'更新文件失败: {e}')
            return False


    def delete_file(self, file_id: str) -> bool:
        """删除文件

        Args:
            file_id: 文件ID

        Returns:
            删除是否成功
        """
        action = 'DeleteFile'
        params = self.create_api_info(action)
        body = {
            'ProjectId': self.project_id,
            'FileId': file_id
        }
        runtime = util_models.RuntimeOptions()
        request = open_api_models.OpenApiRequest(body=body)

        try:
            response = self.client.call_api(params, request, runtime)
            self.logger.info('删除文件成功')
            return True
        except Exception as e:
            self.logger.error(f'删除文件失败: {e}')
            return False

    def submit_file(self, file_id: str) -> bool:
        """提交文件

        Args:
            file_id: 文件ID

        Returns:
            提交是否成功
        """
        request = self.models.SubmitFileRequest(
            project_id=self.project_id,
            file_id=file_id
        )

        try:
            response = self.new_client.submit_file_with_options(request, self.runtime)
            if response.status_code == 200:
                self.logger.info('提交文件成功')
                return True
            else:
                self.logger.error(f'提交文件失败, statusCode: {response.status_code}, error message: {response.body.message}')
                return False
        except Exception as e:
            self.logger.error(f'提交文件失败: {e}')
            return False

    def file_exists(self, file_folder_path: str, file_name: str) -> bool:
        """检查文件是否存在

        Args:
            file_folder_path: 文件夹路径
            file_name: 文件名

        Returns:
            文件是否存在
        """
        request = self.old_models.ListFilesRequest(
            project_id=self.project_id,
            file_folder_path=file_folder_path,
            need_content=False,
            use_type='NORMAL',
            file_types='23',
            page_size=10,
            exact_file_name=file_name
        )

        try:
            response = self.client.list_files_with_options(request, self.runtime)
            if response.status_code == 200:
                return response.body.data.total_count > 0
            else:
                self.logger.error(f'获取文件列表失败, statusCode: {response.status_code}, error message: {response.body.message}')
                return False
        except Exception as e:
            self.logger.error(f'检查文件是否存在失败: {e}')
            return False

    def node_exists(self, file_folder_path: str, file_name: str) -> bool:
        """检查节点是否存在

        Args:
            file_folder_path: 文件夹路径（此参数暂未使用）
            file_name: 文件名

        Returns:
            节点是否存在
        """
        request = self.models.ListNodesRequest(
            project_id=self.project_id,
            recurrence='NORMAL',
            scene='DataworksProject',
            page_size=10,
            name=file_name
        )

        try:
            response = self.new_client.list_nodes_with_options(request, self.runtime)
            if response.status_code == 200:
                return int(response.body.paging_info.total_count) > 0
            else:
                self.logger.error(f'获取节点列表失败, statusCode: {response.status_code}, error message: {response.body.message}')
                return False
        except Exception as e:
            self.logger.error(f'检查节点是否存在失败: {e}')
            return False


    def get_file_list(self, file_folder_path: str, need_content: bool = False) -> List[Tuple[str, str, str, str]]:
        """获取文件列表

        Args:
            file_folder_path: 文件夹路径
            need_content: 是否需要文件内容

        Returns:
            文件信息列表，每个元素为(file_id, file_name, node_id, content)的元组
        """
        request = self.old_models.ListFilesRequest(
            project_id=self.project_id,
            file_folder_path=file_folder_path,
            need_content=need_content,
            use_type='NORMAL',
            file_types='23',
            page_size=100,
            page_number=1
        )

        try:
            response = self.client.list_files_with_options(request, self.runtime)
            if response.status_code != 200:
                self.logger.error(f'获取文件列表失败, statusCode: {response.status_code}, error message: {response.body.message}')
                return []

            total_count = response.body.data.total_count
            page_size = response.body.data.page_size
            page_count = total_count // page_size + 1
            file_id_list = [(file.file_id, file.file_name, file.node_id, file.content)
                           for file in response.body.data.files]

            if total_count <= page_size:
                return file_id_list

            for page_number in range(2, page_count + 1):
                page_request = self.old_models.ListFilesRequest(
                    project_id=self.project_id,
                    file_folder_path=file_folder_path,
                    need_content=need_content,
                    use_type='NORMAL',
                    file_types='23',
                    page_size=page_size,
                    page_number=page_number
                )
                page_response = self.client.list_files_with_options(page_request, self.runtime)
                if page_response.status_code == 200:
                    id_list = [(file.file_id, file.file_name, file.node_id, file.content)
                              for file in page_response.body.data.files]
                    file_id_list.extend(id_list)
            return file_id_list

        except Exception as e:
            self.logger.error(f'获取文件列表失败: {e}')
            return []

    def get_node_list(self, scene: Scene = Scene.project, name: str = '') -> List[Tuple[str, str, str, str]]:
        """获取节点列表

        Args:
            scene: 场景类型
            name: 节点名称

        Returns:
            节点信息列表，每个元素为(node_id, name, task_id, script)的元组
        """
        request = self.models.ListNodesRequest(
            project_id=self.project_id,
            name=name,
            page_size=100,
            page_number=1
        )

        try:
            response = self.new_client.list_nodes_with_options(request, self.runtime)
            if response.status_code != 200:
                self.logger.error(f'获取节点列表失败, statusCode: {response.status_code}, error message: {response.body.message}')
                return []

            total_count = int(response.body.paging_info.total_count)
            page_size = int(response.body.paging_info.page_size)
            page_count = total_count // page_size + 1
            file_id_list = [(node.id, node.name, node.task_id, node.script)
                           for node in response.body.paging_info.nodes]

            if total_count <= page_size:
                return file_id_list

            for page_number in range(2, page_count + 1):
                page_request = self.models.ListNodesRequest(
                    project_id=self.project_id,
                    name=name,
                    page_size=page_size,
                    page_number=page_number
                )
                page_response = self.new_client.list_nodes_with_options(page_request, self.runtime)
                if page_response.status_code == 200:
                    id_list = [(node.id, node.name, node.task_id, node.script)
                              for node in page_response.body.paging_info.nodes]
                    file_id_list.extend(id_list)
            return file_id_list

        except Exception as e:
            self.logger.error(f'获取节点列表失败: {e}')
            return []
    def get_node_content(self, node_id: str) -> Optional[str]:
        """获取节点内容

        Args:
            node_id: 节点ID

        Returns:
            节点规格内容，失败时返回None
        """
        request = self.models.GetNodeRequest(
            project_id=self.project_id,
            id=node_id
        )
        try:
            response = self.new_client.get_node_with_options(request, self.runtime)
            return response.body.node.spec
        except Exception as error:
            self.logger.error(f'获取节点内容失败: {error.message}')
            if hasattr(error, 'data') and error.data:
                self.logger.error(f'诊断地址: {error.data.get("Recommend", "")}')
            return None

    def create_node(self, node_spec: str, node_type: str = 'DATAWORKS_PROJECT') -> Optional[str]:
        """创建节点

        Args:
            node_spec: 节点规格
            node_type: 节点类型

        Returns:
            节点ID，失败时返回None
        """
        request = self.models.CreateNodeRequest(
            project_id=self.project_id,
            scene=node_type,
            spec=node_spec
        )
        try:
            response = self.new_client.create_node_with_options(request, self.runtime)
            self.logger.info(f'创建节点成功, nodeId: {response.body.id}')
            return response.body.id
        except Exception as error:
            self.logger.error(f'创建节点失败: {error.message}')
            if hasattr(error, 'data') and error.data:
                self.logger.error(f'诊断地址: {error.data.get("Recommend", "")}')
            return None

    def check_table_exists(self, table_guid: str) -> Optional[Dict[str, Any]]:
        """检查表是否存在

        Args:
            table_guid: 表GUID

        Returns:
            表信息，失败时返回None
        """
        action = 'CheckMetaTable'
        params = self.create_api_info(action)
        body = {'TableGuid': table_guid}
        runtime = util_models.RuntimeOptions()
        request = open_api_models.OpenApiRequest(body=body)

        try:
            response = self.client.call_api(params, request, runtime)
            return response['body']['Data']
        except Exception as e:
            self.logger.error(f'检查表是否存在失败: {e}')
            return None
    def create_business_if_not_exist(self, business_name: str, business_desc: str = '') -> Optional[str]:
        """如果业务不存在则创建业务

        Args:
            business_name: 业务名称
            business_desc: 业务描述

        Returns:
            业务ID，失败时返回None
        """
        list_business_request = self.models.ListBusinessRequest(
            project_id=self.project_id,
            keyword=business_name,
            page_number=1,
            page_size=5
        )

        try:
            # 先检查业务是否已存在
            list_res = self.client.list_business_with_options(list_business_request)
            if list_res['body']['data']['TotalCount'] > 0:
                for business in list_res['body']['Data']:
                    if UtilClient.equal_string(business['BusinessName'], business_name):
                        self.logger.info(f'业务已存在: {business_name}')
                        return business['BusinessId']

            # 如果不存在则创建
            create_business_request = self.models.CreateBusinessRequest(
                project_id=self.project_id,
                business_name=business_name,
                description=business_desc,
                use_type='NORMAL'
            )
            response = self.client.create_business_with_options(create_business_request, self.runtime)
            business_id = response['body']['BusinessId']
            self.logger.info(f'创建业务成功: {business_name}, BusinessId: {business_id}')
            return business_id

        except Exception as error:
            self.logger.error(f'创建业务失败: {error.message}')
            if hasattr(error, 'data') and error.data:
                self.logger.error(f'诊断地址: {error.data.get("Recommend", "")}')
            return None

    def create_d2_sync_task(self, input_list: Optional[List[str]], file_folder_path: str,
                           file_name: str, src_type: str, src_name: str, src_columns: str,
                           src_table: str, tgt_type: str, tgt_columns: str, tgt_table: str,
                           target_schema: str, target_database: str, is_truncate: bool = True,
                           where: Optional[str] = None) -> Optional[str]:
        """在DataWorks中创建同步任务

        分为三个步骤：create_di_task + update_file + submit_file

        Args:
            input_list: 输入列表
            file_folder_path: 文件夹路径
            file_name: 文件名
            src_type: 源数据源类型
            src_name: 源数据源名称
            src_columns: 源字段列表
            src_table: 源表名
            tgt_type: 目标数据源类型
            tgt_columns: 目标字段列表
            tgt_table: 目标表名
            target_schema: 目标schema
            target_database: 目标数据库
            is_truncate: 是否截断表
            where: WHERE条件

        Returns:
            创建的文件ID，失败时返回None
        """
        # 第一步：创建DI任务
        file_id = self.create_di_task(
            input_list=input_list,
            file_folder_path=file_folder_path,
            file_name=file_name,
            src_type=src_type,
            src_name=src_name,
            src_columns=src_columns,
            src_table=src_table,
            tgt_type=tgt_type,
            tgt_columns=tgt_columns,
            tgt_table=tgt_table,
            target_schema=target_schema,
            target_database=target_database,
            is_truncate=is_truncate,
            where=where
        )

        if file_id:
            self.logger.info(f'创建同步任务成功: {file_name}, FileId: {file_id}')
            # 注意：如需要可以取消注释以下步骤
            # 第二步：更新文件（设置调度参数等）
            # output_name = f"{target_database}.{target_schema}.{tgt_table}".lower()
            # self.update_file(file_id, output_name)

            # 第三步：提交文件
            # self.submit_file(file_id)

        return file_id

def main():
    """主函数，用于测试D2客户端功能"""
    try:
        # 初始化D2客户端
        d2_client = D2()

        # 示例配置
        task_config = {
            'src_type': 'saphana',
            'src_name': 'SAP_saphanadb',
            'src_columns': 'MANDT,KNUMH,KOPOS,KAPPL,KSCHL,KNUMT,STFKZ,KZBZG,KSTBM,KONMS,KSTBW,KONWS,KRECH,KBETR,KONWA,KPEIN,KMEIN,PRSCH,KUMZA,KUMNE,MEINS,MXWRT,GKWRT,PKWRT,FKWRT,RSWRT,KWAEH,UKBAS,KZNEP,KUNNR,LIFNR,MWSK1,LOEVM_KO,ZAEHK_IND,BOMAT,KBRUE,KSPAE,BOSTA,KNUMA_PI,KNUMA_AG,KNUMA_SQ,VALTG,VALDT,ZTERM,ANZAUF,MIKBAS,MXKBAS,KOMXWRT,KLF_STG,KLF_KAL,VKKAL,AKTNR,KNUMA_BO,MWSK2,VERTT,VERTN,VBEWA,MDFLG,KFRST,UASTA',
            'src_table': 'KONP',
            'tgt_type': 'saphana',
            'tgt_columns': 'MANDT,KNUMH,KOPOS,KAPPL,KSCHL,KNUMT,STFKZ,KZBZG,KSTBM,KONMS,KSTBW,KONWS,KRECH,KBETR,KONWA,KPEIN,KMEIN,PRSCH,KUMZA,KUMNE,MEINS,MXWRT,GKWRT,PKWRT,FKWRT,RSWRT,KWAEH,UKBAS,KZNEP,KUNNR,LIFNR,MWSK1,LOEVM_KO,ZAEHK_IND,BOMAT,KBRUE,KSPAE,BOSTA,KNUMA_PI,KNUMA_AG,KNUMA_SQ,VALTG,VALDT,ZTERM,ANZAUF,MIKBAS,MXKBAS,KOMXWRT,KLF_STG,KLF_KAL,VKKAL,AKTNR,KNUMA_BO,MWSK2,VERTT,VERTN,VBEWA,MDFLG,KFRST,UASTA',
            'tgt_table': 'ODS_KONP',
            'target_schema': 'CFSAP',
            'target_database': 'HDW_CFSAP',
            'file_folder_path': '业务流程/hufu/数据集成',
            'file_name': 'PRO_D_H4P_HANA_MM_H4P_SAPHANADB_01_SAPHANADB.KONP_D9'
        }

        # 创建数据集成任务
        node_id = d2_client.create_di_task(
            input_list=[],
            file_folder_path=task_config['file_folder_path'],
            file_name=task_config['file_name'],
            src_type=task_config['src_type'],
            src_name=task_config['src_name'],
            src_columns=task_config['src_columns'],
            src_table=task_config['src_table'],
            tgt_type=task_config['tgt_type'],
            tgt_columns=task_config['tgt_columns'],
            tgt_table=task_config['tgt_table'],
            target_schema=task_config['target_schema'],
            target_database=task_config['target_database']
        )

        if node_id:
            print(f"成功创建数据集成任务，节点ID: {node_id}")
        else:
            print("创建数据集成任务失败")

    except Exception as e:
        print(f"执行失败: {e}")


if __name__ == '__main__':
    main()
