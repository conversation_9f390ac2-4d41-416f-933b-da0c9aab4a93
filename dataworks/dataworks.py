import json
import os
import sys
from enum import Enum

from typing import List

from alibabacloud_dataworks_public20240518.client import Client as dataworks_public20240518Client
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dataworks_public20240518 import models as dataworks_public_20240518_models
from alibabacloud_dataworks_public20200518 import models as dataworks_public_20200518_models
from alibabacloud_dataworks_public20200518.client import Client as dataworks_public20200518Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_openapi.client import Client as OpenApiClient
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_openapi_util.client import Client as OpenApiUtilClient
from json_templates import JsonTemplates
import pystache
import utils
from os import path

class D2:
    credential = CredentialClient()
    config = open_api_models.Config(
        credential=credential
    )
    # Endpoint 请参考 https://api.aliyun.com/product/dataworks-public
    config.endpoint = f'dataworks.cn-hangzhou.aliyuncs.com'
    new_client = dataworks_public20240518Client(config)
    client = dataworks_public20200518Client(config)
    runtime = util_models.RuntimeOptions()
    class Scene(Enum) :
        project = 'DataworksProject' # 项目目录
        manual_workflow= 'DataworksManualWorkflow' #手动工作流
        manual_task ='DataworksManualTask' # 手动任务。


    def __init__(self):
        self.project_id = 322310  # cfbigdata
        self.AutoRerunIntervalMillis = 5*60*1000 # 自动重跑间隔5分钟
        self.RerunMode = 'ALL_ALLOWED' # 运行失败重跑
        self.ResourceGroupIdentifier = 'Serverless_res_group_329073075337474_729685086666945'  # 调度资源组
        self.SchedulerType = 'NORMAL'
        self.CreateFolderIfNotExists = True
        self.models = dataworks_public_20240518_models
        self.old_models = dataworks_public_20200518_models
        self.logger = utils.get_logger(__name__)

    @staticmethod
    def get_job_template(file_type) -> str:
        _job_config = {
            23: 'di', # 数据集成任务
            900:'ri',
            24:'odps_script',
            10012:'saphana',
            99:'virtual',
            0:'spec',

        }
        template_file_name = f'template/{_job_config[file_type]}.json'
        _template_path = path.join(path.dirname(__file__), template_file_name)
        with open(_template_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    @staticmethod
    def create_api_info(action:str) -> open_api_models.Params:
        """
        API 相关
        @param path: string Path parameters
        @return: OpenApi.Params
        """
        params = open_api_models.Params(
            # 接口名称,
            action=action,
            # 接口版本,
            version='2020-05-18',
            # 接口协议,
            protocol='HTTPS',
            # 接口 HTTP 方法,
            method='POST',
            auth_type='AK',
            style='RPC',
            # 接口 PATH,
            pathname=f'/',
            # 接口请求体内容格式,
            req_body_type='formData',
            # 接口响应体内容格式,
            body_type='json'
        )
        return params

    def create_file(self, FileFolderPath: str, FileName: str, file_type: str, content: str, input_list: List[str],connection_name=None) -> str:
        create_file_request = self.models.CreateFileRequest(
            project_id=self.project_id,
            file_name=FileName,
            file_folder_path=FileFolderPath,
            file_type=file_type,
            content=content,
            auto_rerun_times=3,
            auto_rerun_interval_millis = self.AutoRerunIntervalMillis,
            resource_group_identifier=self.ResourceGroupIdentifier,
            create_folder_if_not_exists=self.CreateFolderIfNotExists,
            scheduler_type=self.SchedulerType,
            rerun_mode=self.RerunMode,
            input_list=','.join(input_list) if input_list else 'cfbigdata_root',
            auto_parsing=True,
            connection_name=connection_name
        )
        try:
            # 复制代码运行请自行打印 API 的返回值
            response = self.new_client.create_file_with_options(create_file_request, self.runtime)
            file_id = response.body.data
            self.logger.info(f'create file success, fileId: {file_id}')
            return file_id
        except Exception as error:
            # 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            # 错误 message
            print(error.message)
            # 诊断地址
            print(error.data.get("Recommend"))
            UtilClient.assert_as_string(error.message)

    def create_workflow(self, work_flow_name: str) -> str:
        action = 'CreateBusiness'
        params = self.create_api_info(action)
        body = {
            "businessName": work_flow_name,
            "businessDesc": "test",
            "ProjectId": self.project_id
        }
        request = open_api_models.OpenApiRequest(
            body=body
        )
    # 复制代码运行请自行打印 API 的返回值
    # 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
        response = self.client.call_api(params, request, self.runtime)
        self.logger.info(f'create workflow success')

    def create_di_task(self,input_list: List[str], FileFolderPath: str, FileName: str, src_type,src_name,src_columns, src_table, tgt_type, tgt_columns, tgt_table, target_schema, target_database,is_truncate=True, where=None) -> str:
        file_type = 23 # di
        if '.' in src_table:
            schema, table_name = src_table.split('.')
        di_template = self.get_job_template(file_type)
        di_template['steps'][0]['stepType'] = src_type
        di_template['steps'][0]['parameter']['connection'][0]['datasource']=di_template['steps'][0]['parameter'] ['datasource'] = src_name
        di_template['steps'][0]['parameter']['column'] = src_columns.split(',')
        di_template['steps'][0]['parameter']['connection'][0]['table'] = table_name.split(',')
        if src_type == 'oracle':
            di_template['steps'][0]['parameter']['selectedDatabase'] = schema
            di_template['steps'][0]['parameter']['connection'][0]['table'] = src_table.split(',')

        if where:
            di_template['steps'][0]['parameter']['where'] = where
        di_template['steps'][1]['stepType'] = tgt_type
        di_template['steps'][1]['parameter']['datasource'] = target_database
        di_template['steps'][1]['parameter']['column'] = tgt_columns.split(',')
        di_template['steps'][1]['parameter']['table'] = tgt_table
        di_template['steps'][1]['parameter']['schema'] = target_schema
        di_template['steps'][1]['parameter']['tableComment'] = ''
        di_template['steps'][1]['parameter']['tableComment'] = ''
        if tgt_type == 'odps':
            di_template['steps'][1]['parameter']['partition'] = 'ds=${bizdate}'
        elif tgt_type == 'saphana' and is_truncate:
            truncate_sql = f'truncate table {target_schema}.{tgt_table}'
            di_template['steps'][1]['parameter']['preSql'] = [truncate_sql]

        di_template['extend'] = {} if 'extend' not in di_template else di_template['extend']
        di_template['extend']['mode'] = 'wizard'
        di_template['extend']['resourceGroup'] = self.ResourceGroupIdentifier
        di_template['extend']['cu'] = 0.5
        content = json.dumps(di_template)
        spec = self.get_job_template(0)
        spec['spec']['nodes'][0]['script']['path'] ='/'.join([FileFolderPath,FileName])
        spec['spec']['nodes'][0]['script']['content'] = content
        spec['spec']['nodes'][0]['script']['runtime']['cu'] = '0.5'
        spec['spec']['nodes'][0]['script']['runtime']['commandTypeId'] = 23
        spec['spec']['nodes'][0]['script']['runtime']['command'] = 'DI'
        spec['spec']['nodes'][0]['name'] = FileName

        # 输入输出
        if input_list:
            spec['spec']['nodes'][0]['outputs'] = input_list
        spec['spec']['nodes'][0]['outputs'] = {}
        spec['spec']['nodes'][0]['outputs']['nodeOutputs'] = []
        out_put_name = f"{target_database}.{target_schema}.{tgt_table}"
        node_output = {"data": out_put_name.lower(), "refTableName":out_put_name.lower() }
        spec['spec']['nodes'][0]['outputs']['nodeOutputs'].append(node_output)

        spec = json.dumps(spec, ensure_ascii=False)
        return self.create_node(spec)

    def create_hana_task(self, FileFolderPath: str, FileName: str,content: str ) -> str:
        file_type = 10012
        return self.create_file(FileFolderPath, FileName, file_type, content,input_list=None,connection_name='HDW_CFDATA')


    def create_di_task_old(self, FileFolderPath: str, FileName: str, src_type,src_name,src_columns, src_table, tgt_type, tgt_columns, tgt_table, target_schema, target_database,is_truncate=True,where=None ) -> str:
        action = 'CreateDISyncTask'
        para_config = {}
        para_config['FileFolderPath'] = FileFolderPath
        para_config['ResourceGroup'] = self.ResourceGroupIdentifier
        para_config['Cu'] = 0.5
        queries = {}
        queries['ProjectId'] = self.project_id
        queries['TaskType'] = 'DI_OFFLINE'
        queries['TaskParam'] = json.dumps(para_config)
        queries['TaskName'] = FileName
        # body params
        di_template = self.get_job_template(23)
        di_template['steps'][0]['stepType'] = src_type
        di_template['steps'][0]['parameter']['connection'][0]['datasource']=di_template['steps'][0]['parameter'] ['datasource'] = src_name
        di_template['steps'][0]['parameter']['column'] = src_columns.split(',')
        di_template['steps'][0]['parameter']['connection'][0]['table'] = src_table.split(',')
        if where:
            di_template['steps'][0]['parameter']['where'] = where
        di_template['steps'][1]['stepType'] = tgt_type
        di_template['steps'][1]['parameter']['datasource'] = target_database
        di_template['steps'][1]['parameter']['column'] = tgt_columns.split(',')
        di_template['steps'][1]['parameter']['table'] = tgt_table
        di_template['steps'][1]['parameter']['schema'] = target_schema
        di_template['steps'][1]['parameter']['tableComment'] = ''
        di_template['steps'][1]['parameter']['tableComment'] = ''
        if tgt_type == 'odps':
            di_template['steps'][1]['parameter']['partition'] = 'ds=${bizdate}'
        elif tgt_type == 'saphana' and is_truncate:
            truncate_sql = f'truncate table {target_schema}.{tgt_table}'
            di_template['steps'][1]['parameter']['preSql'] = [truncate_sql]
        body = {}
        body['TaskContent'] = json.dumps(di_template)
        # runtime options
        # runtime = util_models.RuntimeOptions()
        request = self.old_models.CreateDISyncTaskRequest(
            project_id=self.project_id,
            task_type='DI_OFFLINE',
            task_param=json.dumps(para_config,ensure_ascii=False) ,
            task_name= FileName,
            task_content=json.dumps(di_template,ensure_ascii=False)
        )
        # 复制代码运行请自行打印 API 的返回值
        # 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
        try:
            response = self.client.create_disync_task_with_options(request, self.runtime)
            if response.status_code != 200:
                self.logger.error(f'create file failed, statusCode: {response.status_code}, error message: {response.body.message}')
                return None
            file_id = response.body.data.file_id
            self.logger.info(f'create file success, fileId: {file_id}')
            return file_id
        except Exception as e:
            self.logger.error(f'create file failed, error message: {e}')
            return None

    def update_file(self, fileId , out_put_list) -> str:
        action = 'UpdateFile'
        # params = self.create_api_info(action)
        # body['FileName'] = 'sacfa'
        # body['FileDescription'] = 'qttttt'
        # runtime options
        # runtime = util_models.RuntimeOptions()
        request = self.models.UpdateFileRequest(
            auto_rerun_times=3,
            project_id=self.project_id,
            auto_rerun_interval_millis=300000,
            rerun_mode=self.RerunMode,
            stop=False,
            para_value='bizdate=$bizdate',
            cron_express='00 10 00 * * ?',
            cycle_type='DAY',
            # dependent_type='SELF',
            input_list='cfbigdata_root',
            output_list = out_put_list,
            file_id=fileId,
            resource_group_identifier=self.ResourceGroupIdentifier,
            scheduler_type='NORMAL'
        )
        # 复制代码运行请自行打印 API 的返回值
        # 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
        response = self.new_client.update_file_with_options(request, self.runtime)
        if response.status_code == 200:
            self.logger.info(f'update file success')
        else:
            self.logger.error(f'update file failed, statusCode: {response.status_code}, error message: {response.body.message}')


    def delete_file(self, fileId) -> str:
        action = 'DeleteFile'
        params = self.create_api_info(action)
        body = {}
        body['ProjectId'] = self.project_id
        body['FileId'] = fileId
        # runtime options
        runtime = util_models.RuntimeOptions()
        request = open_api_models.OpenApiRequest(
            body=body
        )
        # 复制代码运行请自行打印 API 的返回值
        # 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
        response = self.client.call_api(params, request, runtime)
        self.logger.info(f'delete file success')

    def submit_file(self, fileId) -> str:
        request = self.models.SubmitFileRequest(
            project_id=self.project_id,
            file_id=fileId

        )
        # 复制代码运行请自行打印 API 的返回值
        # 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
        response = self.new_client.submit_file_with_options(request, self.runtime)
        if response.status_code == 200:
            self.logger.info(f'submit file success')
        else:
            self.logger.error(f'submit file failed, statusCode: {response.status_code}, error message: {response.body.message}')

    def file_exists(self,FileFolderPath,FileName) -> str:
        request = self.old_models.ListFilesRequest(
            project_id=self.project_id,
            file_folder_path=FileFolderPath,
            need_content=False,
            use_type='NORMAL',
            file_types='23',
            page_size=10,
            exact_file_name = FileName
        )
        response = self.client.list_files_with_options(request, self.runtime)
        if response.status_code == 200:
            if response.body.data.total_count > 0:
                return True
            else:
                return False

        else:
            self.logger.error(f'get file list failed, statusCode: {response.status_code}, error message: {response.body.message}')


    def node_exists(self,FileFolderPath,FileName) -> str:
        request = self.models.ListNodesRequest(
            project_id=self.project_id,
            recurrence='NORMAL',
            scene='DataworksProject',
            page_size=10,
            name = FileName
        )
        response = self.new_client.list_nodes_with_options(request, self.runtime)
        if response.status_code == 200:
            if int(response.body.paging_info.total_count) > 0:
                return True
            else:
                return False

        else:
            self.logger.error(f'get file list failed, statusCode: {response.status_code}, error message: {response.body.message}')


    def get_file_list(self,FileFolderPath,need_content=False) -> str:
        request = self.old_models.ListFilesRequest(
            project_id=self.project_id,
            file_folder_path=FileFolderPath,
            need_content=need_content,
            use_type='NORMAL',
            file_types='23',
            page_size=100,
            page_number=1

        )

        response = self.client.list_files_with_options(request, self.runtime)
        if response.status_code == 200:
            total_count = response.body.data.total_count
            page_size = response.body.data.page_size
            page_count = total_count // page_size  +  1
            file_id_list = [(file.file_id,file.file_name,file.node_id,file.content) for file in response.body.data.files]
            if total_count <= page_size:
                return file_id_list
            for page_number in range(2,page_count):
                page_request = self.models.ListFilesRequest(
                    project_id=self.project_id,
                    file_folder_path=FileFolderPath,
                    need_content=need_content,
                    use_type='NORMAL',
                    file_types='23',
                    page_size=page_size,
                    page_number=page_number

                )
                response = self.client.list_files_with_options(page_request, self.runtime)
                if response.status_code == 200:
                    id_list = [(file.file_id,file.file_name,file.node_id,file.content)  for file in response.body.data.files]
                    file_id_list.extend(id_list)
            return file_id_list

        else:
            self.logger.error(f'get file list failed, statusCode: {response.status_code}, error message: {response.body.message}')
            return []

    def get_node_list(self, scene = Scene.project, name='') -> str:
        request = self.models.ListNodesRequest(
            project_id=self.project_id,
            name = name,
            page_size=100,
            page_number=1

        )

        response = self.new_client.list_nodes_with_options(request, self.runtime)
        if response.status_code == 200:
            total_count = response.body.paging_info.total_count
            page_size = response.body.paging_info.page_size
            page_count = int(total_count) // int(page_size)  +  1
            file_id_list = [(node.id,node.name,node.task_id,node.script) for node in response.body.paging_info.nodes]
            if int(total_count) <= int(page_size):
                return file_id_list
            for page_number in range(2,page_count):
                page_request = self.models.ListNodesRequest(
                    project_id=self.project_id,
                    name = name,
                    page_size=page_size,
                    page_number=page_number

                )
                response = self.new_client.list_nodes_with_options(page_request, self.runtime)
                if response.status_code == 200:
                    id_list = [(node.id,node.name,node.task_id,node.script)  for node in response.body.paging_info.nodes]
                    file_id_list.extend(id_list)
            return file_id_list

        else:
            self.logger.error(f'get file list failed, statusCode: {response.status_code}, error message: {response.body.message}')
            return []
    def get_node_content(self,node_id):
        request = self.models.GetNodeRequest(
            project_id=self.project_id,
            id=node_id
        )
        try:
            response = self.new_client.get_node_with_options(request, self.runtime)
            return response.body.node.spec
        except Exception as error:
            # 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            # 错误 message
            print(error.message)
            # 诊断地址
            print(error.data.get("Recommend"))
            UtilClient.assert_as_string(error.message)

    def create_node(self, node_spec,node_type= 'DATAWORKS_PROJECT') -> str:
        request = self.models.CreateNodeRequest(
            project_id=self.project_id,
            scene=node_type,
            spec=node_spec
        )
        try:
            response = self.new_client.create_node_with_options(request, self.runtime)
            return response.body.id
        except Exception as error:
            # 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            # 错误 message
            print(error.message)
            # 诊断地址
            print(error.data.get("Recommend"))
            UtilClient.assert_as_string(error.message)

    def check_table_exists(self, TableGuid) -> str:
        action = 'CheckMetaTable'
        params = self.create_api_info(action)
        body = {}
        body['TableGuid'] = TableGuid
        # runtime options
        runtime = util_models.RuntimeOptions()
        request = open_api_models.OpenApiRequest(
            body=body
        )
        # 复制代码运行请自行打印 API 的返回值
        # 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
        response = self.client.call_api(params, request, runtime)
        return response['body']['Data']
    def createBusinessIfNotExist(self, BusinessName, BusinessDesc='') -> str:

        list_business_request = self.models.ListBusinessRequest(
            project_id=self.project_id,
            keyword=BusinessName,
            page_number=1,
            page_size=5
        )
        create_business_request = self.models.CreateBusinessRequest(
            project_id=self.project_id,
            business_name=BusinessName,
            description=BusinessDesc,
            use_type='NORMAL'
        )
        try:
            # 复制代码运行请自行打印 API 的返回值
            list_res = self.client.list_business_with_options(list_business_request)
            if list_res['body']['data']['TotalCount'] > 0:
                for business in list_res['body']['Data']:
                    if UtilClient.equal_string(business['BusinessName'], BusinessName):
                        return business['BusinessId']
            response = self.client.create_business_with_options(create_business_request, self.runtime)
        except Exception as error:
            # 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            # 错误 message
            print(error.message)
            # 诊断地址
            print(error.data.get("Recommend"))
            UtilClient.assert_as_string(error.message)
        # 复制代码运行请自行打印 API 的返回值
        # 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。

        return response['body']['BusinessId']

    def create_d2_sync_task(self, input_list, FileFolderPath, FileName, src_type, src_name,
                            src_columns, src_table, tgt_type, tgt_columns, tgt_table, target_schema, target_database,is_truncate=True,where=None):
        """
        在DataWorks中创建同步任务，分为三个步骤：create_di_task + update_file + submit_file

        Args:
            d2_client: D2客户端实例
            input_list: 输入列表
            FileFolderPath: 文件夹路径
            FileName: 文件名
            src_type: 源数据源类型
            src_name: 源数据源名称
            src_columns: 源字段列表
            src_table: 源表名
            tgt_type: 目标数据源类型
            tgt_columns: 目标字段列表
            tgt_table: 目标表名
            target_schema: 目标schema
            target_database: 目标数据库

        Returns:
            str: 创建的文件ID
        """
        out_put_name = f"{target_database}.{target_schema}.{tgt_table}"
    # 第一步：创建DI任务
        file_id = self.create_di_task(
            input_list=input_list,
            FileFolderPath=FileFolderPath,
            FileName=FileName,
            src_type=src_type,
            src_name=src_name,
            src_columns=src_columns,
            src_table=src_table,
            tgt_type=tgt_type,
            tgt_columns=tgt_columns,
            tgt_table=tgt_table,
            target_schema=target_schema,
            target_database=target_database,
            is_truncate=is_truncate,
            where=where
        )

        # file_id = self.create_di_task(
        #     input_list=[],
        #     FileFolderPath=FileFolderPath,
        #     FileName=FileName,
        #     src_type=src_type,
        #     src_name=src_name,
        #     src_columns=src_columns,
        #     src_table=src_table,
        #     tgt_type=tgt_type,
        #     tgt_columns=tgt_columns,
        #     tgt_table=tgt_table,
        #     target_schema=target_schema,
        #     target_database=target_database
        # )

        # 第二步：更新文件（设置调度参数等）
        # self.update_file(file_id,out_put_name.lower())

        # 第三步：提交文件
        # self.submit_file(file_id)

        return file_id

if __name__ == '__main__':
    D2 = D2()
    src_type = 'saphana'
    src_name = 'SAP_saphanadb'
    src_columns = 'MANDT,KNUMH,KOPOS,KAPPL,KSCHL,KNUMT,STFKZ,KZBZG,KSTBM,KONMS,KSTBW,KONWS,KRECH,KBETR,KONWA,KPEIN,KMEIN,PRSCH,KUMZA,KUMNE,MEINS,MXWRT,GKWRT,PKWRT,FKWRT,RSWRT,KWAEH,UKBAS,KZNEP,KUNNR,LIFNR,MWSK1,LOEVM_KO,ZAEHK_IND,BOMAT,KBRUE,KSPAE,BOSTA,KNUMA_PI,KNUMA_AG,KNUMA_SQ,VALTG,VALDT,ZTERM,ANZAUF,MIKBAS,MXKBAS,KOMXWRT,KLF_STG,KLF_KAL,VKKAL,AKTNR,KNUMA_BO,MWSK2,VERTT,VERTN,VBEWA,MDFLG,KFRST,UASTA'
    src_table = 'KONP'
    tgt_type = 'saphana'
    # tgt_name = 'cfbigdata'
    tgt_columns = 'MANDT,KNUMH,KOPOS,KAPPL,KSCHL,KNUMT,STFKZ,KZBZG,KSTBM,KONMS,KSTBW,KONWS,KRECH,KBETR,KONWA,KPEIN,KMEIN,PRSCH,KUMZA,KUMNE,MEINS,MXWRT,GKWRT,PKWRT,FKWRT,RSWRT,KWAEH,UKBAS,KZNEP,KUNNR,LIFNR,MWSK1,LOEVM_KO,ZAEHK_IND,BOMAT,KBRUE,KSPAE,BOSTA,KNUMA_PI,KNUMA_AG,KNUMA_SQ,VALTG,VALDT,ZTERM,ANZAUF,MIKBAS,MXKBAS,KOMXWRT,KLF_STG,KLF_KAL,VKKAL,AKTNR,KNUMA_BO,MWSK2,VERTT,VERTN,VBEWA,MDFLG,KFRST,UASTA'
    tgt_table = 'ODS_KONP'
    target_schema = 'CFSAP'
    target_database = 'HDW_CFSAP'
    fileFolderPath = '业务流程/hufu/数据集成'
    fileName = 'PRO_D_H4P_HANA_MM_H4P_SAPHANADB_01_SAPHANADB.KONP_D9'
    config = {
        "src_type" : 'saphana',
    'src_name' : 'Sap',
    'src_columns' : 'fnumber',
    'src_table' : 'V_MATERIAL',
    'tgt_type' : 'odps',
    # tgt_name = 'cfbigdata',
    'tgt_columns' : 'fnumber',
    'tgt_table': 'ods_v_material',
    'target_schema' : 'cfsap',
    'target_database' : 'cfdata'
    }
    with open('template/di.json', 'r', encoding='utf-8') as f:
        di_template = json.load(f)

    # res = pystache.render(di_template, config)
    node_id = D2.create_di_task([],fileFolderPath, fileName, src_type, src_name, src_columns, src_table, tgt_type, tgt_columns, tgt_table, target_schema,target_database)
    # D2.get_node_content(5739068818160748755)
    # file_list = D2.get_file_list(fileFolderPath,True)
    # for file_id,file_name,node_id,content in file_list:
    #     file_content = json.loads(content)
    #     if file_content['setting']['errorLimit'] == 0 or file_content['setting']['errorLimit']['record'] == '':
    #         file_content['setting']['errorLimit'] = {'record':'0'}
    #     schema = file_content['steps'][1]['parameter']['schema'].lower()
    #     table = file_content['steps'][1]['parameter']['table'].lower()
    #
    #     request = D2.old_models.UpdateFileRequest(
    #             auto_rerun_times=3,
    #             project_id=D2.project_id,
    #             auto_rerun_interval_millis=300000,
    #             rerun_mode=D2.RerunMode,
    #             stop=False,
    #             para_value='bizdate=$bizdate',
    #             cron_express='00 10 00 * * ?',
    #             cycle_type='DAY',
    #             # dependent_type='SELF',
    #             input_list='cfbigdata_root',
    #             output_list=f'cfdata.{schema}.{table}',
    #             file_id=file_id,
    #             resource_group_identifier=D2.ResourceGroupIdentifier,
    #             scheduler_type='NORMAL',
    #             content=json.dumps(file_content)
    #     )
    #     response = D2.client.update_file_with_options(request, D2.runtime)
    #     if response.status_code == 200:
    #         print(f'{file_name} 已更新')
    #         D2.submit_file(file_id)

        # D2.update_file(file_id)
